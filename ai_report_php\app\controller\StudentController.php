<?php
namespace app\controller;

use app\model\Student;
use app\model\StudentTag;
use app\model\StudentDetail;
use app\model\School;
use app\model\SchoolMajor;
use app\model\Major;
use app\model\Tag;
use app\model\SchoolReport;
use support\Request;
use support\Log;
use app\model\SchoolInfo;
use app\model\User;
use think\facade\Db;
use app\model\SecondMajor;
use app\model\FirstLevelDisciplineCode;
use app\controller\RemoteController;
class StudentController
{
    /**
     * 获取学生列表
     * @param Request $request
     * @return \support\Response
     */
    public function getList(Request $request)
    {
        // 记录请求参数
        $requestParams = $request->all();
        Log::info('获取学生列表请求参数: ' . json_encode($requestParams));

        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $name = $request->get('name', '');
        $phone = $request->get('phone', '');
        $teacher = $request->get('teacher', '');
        $teacherId = $request->get('teacherId', ''); // 添加教师ID参数
        $school = $request->get('school', '');
        $undergraduate = $request->get('undergraduate', '');
        $undergraduateMajor = $request->get('undergraduateMajor', '');
        $targetSchool = $request->get('targetSchool', '');
        $targetMajor = $request->get('targetMajor', '');

        // 处理标签ID参数 - 支持数组格式
        $tagIds = $request->get('tagId');
        // 确保tagIds是数组格式
        if (!is_array($tagIds)) {
            if (empty($tagIds)) {
                $tagIds = [];
            } else {
                $tagIds = [$tagIds];
            }
        }
        // 过滤掉空值
        $tagIds = array_filter($tagIds, function($value) {
            return !empty($value);
        });

        try {
            // 构建查询条件
            $query = Student::alias('s')
                ->leftJoin('ba_user u', 's.admin_id = u.id')
                ->where('s.is_delete', 0);

        // 添加筛选条件
        if (!empty($name)) {
            $query->where('s.name', 'like', "%{$name}%");
        }
        if (!empty($phone)) {
            $query->where('s.phone', 'like', "%{$phone}%");
        }

        // 优先使用教师ID进行精确搜索
        if (!empty($teacherId)) {
            $query->where('s.admin_id', $teacherId);
        }
        // 如果没有教师ID但有教师名称，则使用模糊搜索
        else if (!empty($teacher)) {
            $query->where(function($q) use ($teacher) {
                $q->where('s.teacher_phone', 'like', "%{$teacher}%")
                  ->whereOr('u.username', 'like', "%{$teacher}%")
                  ->whereOr('u.nickname', 'like', "%{$teacher}%");
            });
        }
        if (!empty($school)) {
            $query->where('s.undergraduate_school_name', 'like', "%{$school}%");
        }
        if (!empty($undergraduate)) {
            $query->where('s.undergraduate_school_name', 'like', "%{$undergraduate}%");
        }
        if (!empty($undergraduateMajor)) {
            $query->where('s.undergraduate_major_name', 'like', "%{$undergraduateMajor}%");
        }
        if (!empty($targetSchool)) {
            // 联表查询详情表中的目标学校
            $query->join('ba_student_detail d', 's.id = d.student_id')
                ->where('d.target_school_name', 'like', "%{$targetSchool}%");
        }
        if (!empty($targetMajor)) {
            $query->where('s.target_major_name', 'like', "%{$targetMajor}%");
        }

        // 添加标签筛选
        if (!empty($tagIds)) {
            // 查询具有指定标签的学生ID
            $studentIdsWithTag = StudentTag::whereIn('tag_id', $tagIds)
                ->column('student_id');

            if (!empty($studentIdsWithTag)) {
                $query->whereIn('s.id', $studentIdsWithTag);
            } else {
                // 如果没有找到匹配的学生，返回空结果
                return json([
                    'code' => 0,
                    'msg' => 'success',
                    'data' => [],
                    'total' => 0
                ]);
            }
        }

        $query->where("admin_id", $request->user->uid);
        // 选择需要的字段
        $query->field([
            's.*',
            'u.username as teacher_username',
            'u.nickname as teacher_nickname'
        ]);

        // 计算总数
        $total = $query->count();

        // 分页查询 - 使用兼容的分页方法
        // 注意：ThinkPHP不同版本的分页方法可能不同
        // 方法1：使用page方法
        $students = $query->page($page, $limit)->select();
        // 如果上面的方法不起作用，可以尝试下面的方法
        // 方法2：使用limit方法的两个参数形式
        // $offset = ($page - 1) * $limit;
        // $students = $query->limit($offset, $limit)->select();

        // 获取学生标签
        $studentIds = array_column($students->toArray(), 'id');
        $studentTags = [];
        if (!empty($studentIds)) {
            $tagRelations = StudentTag::whereIn('student_id', $studentIds)->select();
            $tagIds = array_column($tagRelations->toArray(), 'tag_id');

            if (!empty($tagIds)) {
                $tags = Tag::whereIn('id', $tagIds)->select();
                $tagsMap = [];
                foreach ($tags as $tag) {
                    $tagsMap[$tag['id']] = [
                        'value' => $tag['id'],
                        'label' => $tag['name'],
                        'class' => 'tag-' . $tag['color']
                    ];
                }

                foreach ($tagRelations as $relation) {
                    $studentId = $relation['student_id'];
                    $tagId = $relation['tag_id'];
                    if (isset($tagsMap[$tagId])) {
                        $studentTags[$studentId][] = $tagsMap[$tagId];
                    }
                }
            }
        }

        // 处理返回数据
        $result = [];
        foreach ($students as $student) {
            // 确定老师名称，优先使用昵称，如果没有则使用用户名
            $teacherName = '';
            if (!empty($student['teacher_nickname'])) {
                $teacherName = $student['teacher_nickname'];
            } elseif (!empty($student['teacher_username'])) {
                $teacherName = $student['teacher_username'];
            }

            // 获取本科院校信息
            $undergraduateSchoolName = $student['undergraduate_school_name'];
            if (!empty($student['undergraduate_school_id'])) {
                $school = School::where('id', $student['undergraduate_school_id'])->where('is_delete', 0)->find();
                if ($school) {
                    $undergraduateSchoolName = $school['name'];
                }
            }

            // 获取本科专业信息
            $undergraduateMajorName = $student['undergraduate_major_name'];
            if (!empty($student['undergraduate_major_id'])) {
                $major = SchoolMajor::where('id', $student['undergraduate_major_id'])->where('is_delete', 0)->find();
                if ($major) {
                    $undergraduateMajorName = $major['major_name'];
                }
            }
            // 获取目标专业信息
            $targetMajorName = $student['target_major_name'];
            if (!empty($student['target_major_id'])) {
                $targetMajor = Major::whereIn('id', explode(',', $student['target_major_id']))->where('is_delete', 0)->column("erji_value");
                if ($targetMajor) {
                    $targetMajorName = implode(',', $targetMajor);
                }
            }

            // 获取梦校信息
            $targetSchoolName = '';
            // 获取学生详情以获取梦校ID
            $detail = StudentDetail::where('student_id', $student['id'])->find();
            if ($detail && !empty($detail['target_school_id'])) {
                $targetSchool = School::where('id', $detail['target_school_id'])->where('is_delete', 0)->find();
                if ($targetSchool) {
                    $targetSchoolName = $targetSchool['name'];
                } else {
                    $targetSchoolName = $detail['target_school_name'] ?? '';
                }
            }
            $item = [
                'id' => $student['id'],
                'name' => $student['name'],
                'sex' => $student['sex'],
                'sexText' => $student['sex_text'],
                'phone' => $student['phone'],
                'teacherPhone' => $student['teacher_phone'] ?? '',
                'teacherName' => $teacherName,
                'adminId' => $student['admin_id'],
                'undergraduateSchool' => $student['undergraduate_school_id'],
                'undergraduateSchoolName' => $undergraduateSchoolName,
                'undergraduateMajor' => $student['undergraduate_major_id'],
                'undergraduateMajorName' => $undergraduateMajorName,
                'targetMajor' => $student['target_major_id'],
                'targetMajorName' => $targetMajorName,
                'targetSchoolName' => $targetSchoolName,
                'majorCode' => $student['major_code'],
                'isPostgraduate' => (bool)$student['is_postgraduate'],
                'isPostgraduateText' => $student['is_postgraduate_text'],
                'examYear' => $student['exam_year'],
                'isMultiDisciplinary' => $student['is_multi_disciplinary'],
                'isMultiDisciplinaryText' => $student['is_multi_disciplinary_text'],
                'tags' => $studentTags[$student['id']] ?? []
            ];
            $result[] = $item;
        }

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $result,
            'total' => $total
        ]);
        } catch (\Exception $e) {
            Log::error('获取学生列表异常: ' .  $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            return json([
                'code' => 500,
                'msg' => '获取学生列表失败: ' . $e->getMessage(),
                'data' => [],
                'total' => 0
            ]);
        }
    }

    /**
     * 获取学生详情
     * @param Request $request
     * @return \support\Response
     */
    public function getDetail(Request $request)
    {
        $id = $request->get('id', 0);
        if (empty($id)) {
            return json([
                'code' => 400,
                'msg' => '参数错误',
                'data' => []
            ]);
        }

        // 获取学生基本信息
        $student = Student::where('id', $id)->where('is_delete', 0)->find();
        if (!$student) {
            return json([
                'code' => 404,
                'msg' => '学生不存在',
                'data' => []
            ]);
        }

        // 获取学生详细信息
        $detail = StudentDetail::where('student_id', $id)->find();

        // 获取学生标签
        $tagRelations = StudentTag::where('student_id', $id)->select();
        $tagIds = array_column($tagRelations->toArray(), 'tag_id');
        $tags = [];
        if (!empty($tagIds)) {
            $tagList = Tag::whereIn('id', $tagIds)->select();
            foreach ($tagList as $tag) {
                $tags[] = [
                    'value' => $tag['id'],
                    'label' => $tag['name'],
                    'class' => 'tag-' . $tag['color']
                ];
            }
        }

        // 获取本科院校信息
        $undergraduateSchoolName = $student['undergraduate_school_name'];
        if (!empty($student['undergraduate_school_id'])) {
            $school = School::where('id', $student['undergraduate_school_id'])->where('is_delete', 0)->find();
            if ($school) {
                $undergraduateSchoolName = $school['name'];
            }
        }

        // 获取本科专业信息
        $undergraduateMajorName = $student['undergraduate_major_name'];
        if (!empty($student['undergraduate_major_id'])) {
            $major = SchoolMajor::where('id', $student['undergraduate_major_id'])->where('is_delete', 0)->find();
            if ($major) {
                $undergraduateMajorName = $major['major_name'];
            }
        }

        // 获取目标专业信息（支持多选）
        $targetMajorIds = $student['target_major_id'] ? explode(',', $student['target_major_id']) : [];

        array_walk($targetMajorIds, function(&$value) {
            if($value !=="*"){
                $value = intval(trim($value));
            }
        });
        $targetMajorNames = $student['target_major_name'] ? explode(',', $student['target_major_name']) : [];

        // 如果数据库中存储的是专业名称，直接使用；否则根据ID查询
        if (empty($targetMajorNames) && !empty($targetMajorIds)) {
            $targetMajorNames = [];
            foreach ($targetMajorIds as $majorId) {
                $targetMajor = Major::where('id', trim($majorId))->where('is_delete', 0)->find();
                if ($targetMajor) {
                    $targetMajorNames[] = $targetMajor['name'];
                } else {
                    $targetMajorNames[] = "专业ID: " . $majorId;
                }
            }
        }

        // 构建返回数据
        $result = [
            'id' => $student['id'],
            'name' => $student['name'],
            'sex' => $student['sex'],
            'sexText' => $student['sex_text'],
            'phone' => $student['phone'],
            'teacherPhone' => $student['teacher_phone'] ?? '',
            'undergraduateSchool' => $student['undergraduate_school_id'],
            'undergraduateSchoolName' => $undergraduateSchoolName,
            'undergraduateMajor' => $student['undergraduate_major_id'],
            'undergraduateMajorName' => $undergraduateMajorName,
            'targetMajor' => $targetMajorIds, // 返回数组
            'targetMajorName' => $targetMajorNames, // 返回数组
            'majorCode' => $student['major_code'],
            // 添加学科门类和一级学科字段
            'disciplineCategory' => $student['discipline_category_id'],
            'firstLevelDiscipline' => $student['first_level_discipline_id'],
            'isPostgraduate' => (bool)$student['is_postgraduate'],
            'isPostgraduateText' => $student['is_postgraduate_text'],
            'examYear' => $student['exam_year'],
            'isMultiDisciplinary' => $student['is_multi_disciplinary'],
            'isMultiDisciplinaryText' => $student['is_multi_disciplinary_text'],
            'educationalStyle' => $student['educational_style'],
            'educationalStyleText' => $student['educational_style_text'],
            'tags' => $tags
        ];

        // 如果有详细信息，添加到结果中
        if ($detail) {
            // 处理JSON数据
            $undergraduateTranscript = is_string($detail['undergraduate_transcript']) ? json_decode($detail['undergraduate_transcript'], true) : $detail['undergraduate_transcript'];
            $estimatedScores = is_string($detail['estimated_scores']) ? json_decode($detail['estimated_scores'], true) : $detail['estimated_scores'];
            // 处理省份选择 - 确保是字符串再进行分割
            $targetProvinces = [];
            if (!empty($detail['target_provinces'])) {
                if (is_string($detail['target_provinces'])) {
                    $targetProvinces = explode(',', $detail['target_provinces']);
                } elseif (is_array($detail['target_provinces'])) {
                    $targetProvinces = $detail['target_provinces'];
                }
            }

            // 获取梦校信息
            $targetSchoolName = $detail['target_school_name'];
            if (!empty($detail['target_school_id'])) {
                $targetSchool = School::where('id', $detail['target_school_id'])->where('is_delete', 0)->find();
                if ($targetSchool) {
                    $targetSchoolName = $targetSchool['name'];
                }
            }

            $result = array_merge($result, [
                'undergraduateTranscript' => $undergraduateTranscript,
                'englishScore' => $detail['english_score'] ?? '',
                'cet4' => $detail['cet4'] ?? '',
                'cet6' => $detail['cet6'] ?? '',
                'specialEnglishLevel' => $detail['special_english_level'] ?? '',
                'tofelScore' => $detail['tofel_score'] ?? '',
                'ieltsScore' => $detail['ielts_score'] ?? '',
                'englishAbility' => $detail['english_ability'] ?? '',
                'targetRegion' => $detail['target_region'] ? explode(',', $detail['target_region'])  : [],
                'targetProvinces' => $targetProvinces,
                'targetSchool' => $detail['target_school_id'],
                'targetSchoolName' => $targetSchoolName,
                'schoolLevel' => $detail['school_level'] ? explode(',', $detail['school_level']) : [],
                'referenceBooks' => $detail['reference_books'] ?? '',
                // 考研成绩字段映射：politics=政治，english_s=英语，english_type=业务课一，math_type=业务课二
                'politics' => $detail['politics'] ?? '',
                'englishType' => $detail['english_type'] ?? '',     // 业务课一
                'mathType' => $detail['math_type'] ?? '',           // 业务课二
                'englishS'=>$detail['english_s'] ?? '',             // 英语成绩
                'mathScore'=>$detail['math_score'] ?? '',
                'professionalScore'=>$detail['professional_score'] ?? '',
                'estimatedScores' => $estimatedScores,
                'totalScore' => $detail['total_score'] ?? '',
                'personalNeeds' => $detail['personal_needs'] ?? '',
                'weakModules' => $detail['weak_modules'] ?? ''
            ]);
        }

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $result
        ]);
    }

    /**
     * 添加学生
     * @param Request $request
     * @return \support\Response
     */
    public function add(Request $request)
    {
        $data = $request->post();

        // 记录当前登录用户信息，用于调试
        Log::info('当前登录用户: ' . json_encode($request->user));

        // 验证必填字段
        if (empty($data['name'])) {
            return json([
                'code' => 400,
                'msg' => '学生姓名不能为空',
                'data' => []
            ]);
        }

        if (empty($data['phone'])) {
            return json([
                'code' => 400,
                'msg' => '联系方式不能为空',
                'data' => []
            ]);
        }

        // 开启事务
        \think\facade\Db::startTrans();
        try {
            // 创建学生基本信息
            $student = new Student;
            $student->name = $data['name'];
            // 性别：1-男，2-女，3-其他
            $student->sex = in_array($data['sex'], ['1', '2', '3']) ? $data['sex'] : '1';
            $student->phone = $data['phone'];
            $student->teacher_phone = $data['teacherPhone'] ?? '';
            $student->undergraduate_school_id = $data['undergraduateSchool'] ?? 0;
            $student->undergraduate_school_name = $data['undergraduateSchoolName'] ?? '';
            $student->undergraduate_major_id = $data['undergraduateMajor'] ?? 0;
            $student->undergraduate_major_name = $data['undergraduateMajorName'] ?? '';
            // 处理目标专业多选
            $targetMajorIds = is_array($data['targetMajor']) ? $data['targetMajor'] : [$data['targetMajor']];
            $targetMajorIds = array_filter($targetMajorIds); // 过滤空值
            $student->target_major_id = implode(',', $targetMajorIds);

            // 处理目标专业名称
            $targetMajorNames = $data['targetMajorName'] ?? '';
            if (is_array($targetMajorNames)) {
                $student->target_major_name = implode(',', $targetMajorNames);
            } else {
                $student->target_major_name = $targetMajorNames;
            }

            // 处理专业代码
            $majorCodes = $data['majorCode'] ?? [];
            if (is_string($majorCodes)) {
                $majorCodes = explode(',', $majorCodes);
            }
            if(in_array("*", $majorCodes)){
                $majorCodes = ["*"];
            }
            $student->major_code = implode(',', array_filter($majorCodes)); // 确保专业代码是字符串类型
            // 添加学科门类和一级学科字段
            $student->discipline_category_id = $data['disciplineCategory'] ?? null;
            $student->first_level_discipline_id = $data['firstLevelDiscipline'] ?? null;
            $student->is_postgraduate = isset($data['isPostgraduate']) && $data['isPostgraduate'] ? 1 : 0;
            $student->exam_year = $data['examYear'] ?? '';
            $student->is_multi_disciplinary = $data['isMultiDisciplinary'] ?? 0; // 确保跨专业字段类型一致
            $student->educational_style = $data['educationalStyle'] ?? 0; // 确保培养方式字段类型一致
            // 设置为当前登录的管理员ID
            $student->admin_id = $request->user->uid ?? 0;
            $student->create_time = time();
            $student->update_time = time();
            $student->save();

            $studentId = $student->id;

            // 创建学生详细信息
            $detail = new StudentDetail;
            $detail->student_id = $studentId;

            // 处理本科成绩
            // 数据格式: [{"id": 1, "title": "课程名称", "score": "90"}, ...]
            $detail->undergraduate_transcript = isset($data['undergraduateTranscriptJson']) && !empty($data['undergraduateTranscriptJson'])
                ? $data['undergraduateTranscriptJson']
                : json_encode($data['undergraduateTranscript'] ?? [], JSON_UNESCAPED_UNICODE);

            // 处理英语信息
            $detail->english_score = $data['englishScore'] ?? '';
            $detail->cet4 = $data['cet4'] ?? '';
            $detail->cet6 = $data['cet6'] ?? '';
            $detail->tofel_score = $data['tofelScore'] ?? '';
            $detail->ielts_score = $data['ieltsScore'] ?? '';
            $detail->english_ability = $data['englishAbility'] ?? '';

            // 处理目标院校信息
            $detail->target_region = $data['region'] ? implode(',', $data['region']) : '';



            // 数据格式: "北京市,上海市,广东省,..."
            $detail->target_provinces = is_array($data['targetProvinces'] ?? [])
                ? implode(',', $data['targetProvinces'])
                : $data['targetProvinces'] ?? '';
            $detail->target_school_id = $data['targetSchool'] ?? 0;
            $detail->target_school_name = $data['targetSchoolName'] ?? '';
            $detail->school_level = $data['schoolLevel'] ?? '';
            $detail->reference_books = $data['referenceBooks'] ?? '';

            // 处理考研成绩预估
            // 字段映射说明：politics=政治，english_s=英语，english_type=业务课一，math_type=业务课二
            $detail->politics = $data['politics'] ?? '';
            $detail->english_type = $data['englishType'] ?? '';  // 业务课一
            $detail->math_type = $data['mathType'] ?? '';        // 业务课二
            $detail->english_s = $data['englishS'] ?? '';        // 英语成绩
            $detail->math_score = $data['mathScore'] ?? '';
            $detail->professional_score = $data['professionalScore'] ?? '';
            // 数据格式: [{"id": 1, "title": "专业课一", "name": "数据结构", "score": "85"}, ...]
            $detail->total_score = $data['totalScore'] ?? '';

            // 处理个性化需求
            $detail->personal_needs = $data['personalNeeds'] ?? '';

            // 处理薄弱模块
            $detail->weak_modules = $data['weakModules'] ?? '';

            $detail->create_time = time();
            $detail->update_time = time();
            $detail->save();

            // 处理标签
            $tags = $data['tags'] ?? [];
            if (!empty($tags)) {
                $tagData = [];
                foreach ($tags as $tagId) {
                    // 确保标签ID是整数且大于0
                    $tagId = intval($tagId);
                    if ($tagId > 0) {
                        $tagData[] = [
                            'student_id' => $studentId,
                            'tag_id' => $tagId,
                            'create_time' => time()
                        ];
                    }
                }

                if (!empty($tagData)) {
                    // 记录日志，用于调试
                    Log::info('添加学生标签: ' . json_encode($tagData));
                    StudentTag::insertAll($tagData);
                }
            }

            \think\facade\Db::commit();

            return json([
                'code' => 0,
                'msg' => '添加成功',
                'data' => ['id' => $studentId]
            ]);
        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            return json([
                'code' => 500,
                'msg' => '添加失败：' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 编辑学生
     * @param Request $request
     * @return \support\Response
     */
    public function edit(Request $request)
    {
        $data = $request->post();
        // 记录当前登录用户信息和学生信息，用于调试
        Log::info('编辑学生 - 当前登录用户: ' . json_encode($request->user));
        // 验证必填字段
        if (empty($data['id'])) {
            return json([
                'code' => 400,
                'msg' => '学生ID不能为空',
                'data' => []
            ]);
        }

        if (empty($data['name'])) {
            return json([
                'code' => 400,
                'msg' => '学生姓名不能为空',
                'data' => []
            ]);
        }

        if (empty($data['phone'])) {
            return json([
                'code' => 400,
                'msg' => '联系方式不能为空',
                'data' => []
            ]);
        }

        // 查找学生
        $student = Student::where('id', $data['id'])->where('is_delete', 0)->find();
        if (!$student) {
            return json([
                'code' => 404,
                'msg' => '学生不存在',
                'data' => []
            ]);
        }

        // 开启事务
        \think\facade\Db::startTrans();
        try {
            // 更新学生基本信息
            $student->name = $data['name'];
            // 性别：1-男，2-女，3-其他
            $student->sex = in_array($data['sex'], ['1', '2', '3']) ? $data['sex'] : '1';
            $student->phone = $data['phone'];
            $student->teacher_phone = $data['teacherPhone'] ?? '';
            $student->undergraduate_school_id = $data['undergraduateSchool'] ?? 0;
            $student->undergraduate_school_name = $data['undergraduateSchoolName'] ?? '';
            $student->undergraduate_major_id = $data['undergraduateMajor'] ?? 0;
            $student->undergraduate_major_name = $data['undergraduateMajorName'] ?? '';

            // 处理目标专业多选
            $targetMajorIds = is_array($data['targetMajor']) ? $data['targetMajor'] : [$data['targetMajor']];
            $targetMajorIds = array_filter($targetMajorIds); // 过滤空值

            // 检查是否包含*（采用一级学科）
            if(in_array("*", $targetMajorIds)){
                $targetMajorIds = ["*"];
                $data['majorCode'] = "*";
                $data['targetMajorName'] = "采用一级学科";
            }

            $student->target_major_id = implode(',', $targetMajorIds);

            // 处理目标专业名称
            $targetMajorNames = $data['targetMajorName'] ?? '';
            if (is_array($targetMajorNames)) {
                $student->target_major_name = implode(',', $targetMajorNames);
            } else {
                $student->target_major_name = $targetMajorNames;
            }

            // 处理专业代码
            $majorCodes = $data['majorCode'] ?? [];
            if (is_string($majorCodes)) {
                $majorCodes = explode(',', $majorCodes);
            }
            $student->major_code = implode(',', array_filter($majorCodes)); // 确保专业代码是字符串类型


            // 添加学科门类和一级学科字段
            $student->discipline_category_id = $data['disciplineCategory'] ?? null;
            $student->first_level_discipline_id = $data['firstLevelDiscipline'] ?? null;
            $student->is_postgraduate = isset($data['isPostgraduate']) && $data['isPostgraduate'] ? 1 : 0;
            $student->exam_year = $data['examYear'] ?? '';
            $student->is_multi_disciplinary = $data['isMultiDisciplinary'] ?? 0; // 确保跨专业字段类型一致
            $student->educational_style = $data['educationalStyle'] ?? 0; // 确保培养方式字段类型一致
            $student->update_time = time();
            $student->save();

            // 查找或创建学生详细信息
            $detail = StudentDetail::where('student_id', $data['id'])->find();
            if (!$detail) {
                $detail = new StudentDetail;
                $detail->student_id = $data['id'];
                $detail->create_time = time();
            }

            // 处理本科成绩
            // 数据格式: [{"id": 1, "title": "课程名称", "score": "90"}, ...]
            $detail->undergraduate_transcript = isset($data['undergraduateTranscriptJson']) && !empty($data['undergraduateTranscriptJson'])
                ? $data['undergraduateTranscriptJson']
                : json_encode($data['undergraduateTranscript'] ?? [], JSON_UNESCAPED_UNICODE);

            // 处理英语信息
            $detail->english_score = $data['englishScore'] ?? '';
            $detail->cet4 = $data['cet4'] ?? '';
            $detail->cet6 = $data['cet6'] ?? '';
            $detail->tofel_score = $data['tofelScore'] ?? '';
            $detail->ielts_score = $data['ieltsScore'] ?? '';
            $detail->english_ability = $data['englishAbility'] ?? '';

            // 处理目标院校信息
            $detail->target_region = $data['region'] ? implode(',', $data['region']) : '';
            // 数据格式: "北京市,上海市,广东省,..."
            $detail->target_provinces = implode(',', $data['targetProvinces']);
            $detail->target_school_id = $data['targetSchool'] ?? 0;
            $detail->target_school_name = $data['targetSchoolName'] ?? '';
            $detail->school_level = implode(',', $data['schoolLevel']);
            $detail->reference_books = $data['referenceBooks'] ?? '';

            // 处理考研成绩预估
            // 字段映射说明：politics=政治，english_s=英语，english_type=业务课一，math_type=业务课二
            $detail->politics = $data['politics'] ?? '';
            $detail->english_type = $data['englishType'] ?? '';  // 业务课一
            $detail->math_type = $data['mathType'] ?? '';        // 业务课二
            $detail->english_s = $data['englishS'] ?? '';        // 英语成绩
            $detail->professional_score = $data['professionalScore'] ?? '';
            $detail->math_score = $data['mathScore'] ?? '';
            $detail->total_score = $data['totalScore'] ?? '';

            // 处理个性化需求
            $detail->personal_needs = $data['personalNeeds'] ?? '';

            // 处理薄弱模块
            $detail->weak_modules = $data['weakModules'] ?? '';

            $detail->update_time = time();
            $detail->save();

            // 处理标签
            // 先删除原有标签关联
            StudentTag::where('student_id', $data['id'])->delete();

            // 添加新标签关联
            $tags = $data['tags'] ?? [];
            if (!empty($tags)) {
                $tagData = [];
                foreach ($tags as $tagId) {
                    // 确保标签ID是整数且大于0
                    $tagId = intval($tagId);
                    if ($tagId > 0) {
                        $tagData[] = [
                            'student_id' => $data['id'],
                            'tag_id' => $tagId,
                            'create_time' => time()
                        ];
                    }
                }

                if (!empty($tagData)) {
                    // 记录日志，用于调试
                    Log::info('添加学生标签: ' . json_encode($tagData));
                    StudentTag::insertAll($tagData);
                }
            }

            \think\facade\Db::commit();

            return json([
                'code' => 0,
                'msg' => '更新成功',
                'data' => ['id' => $data['id']]
            ]);
        } catch (\Exception $e) {
            // 记录异常信息
            Log::error('更新学生信息异常: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            \think\facade\Db::rollback();
            return json([
                'code' => 500,
                'msg' => '更新失败：' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 删除学生
     * @param Request $request
     * @return \support\Response
     */
    public function delete(Request $request)
    {
        $id = $request->get('id', 0);
        if (empty($id)) {
            return json([
                'code' => 400,
                'msg' => '参数错误',
                'data' => []
            ]);
        }

        // 查找学生
        $student = Student::where('id', $id)->where('is_delete', 0)->find();
        if (!$student) {
            return json([
                'code' => 404,
                'msg' => '学生不存在',
                'data' => []
            ]);
        }

        // 软删除
        $student->is_delete = 1;
        $student->update_time = time();
        $student->save();

        return json([
            'code' => 0,
            'msg' => '删除成功',
            'data' => []
        ]);
    }

    /**
     * 测试接口
     * @param Request $request
     * @return \support\Response
     */
    public function test(Request $request)
    {
        // 获取当前登录用户信息
        $userInfo = $request->user;

        // 获取用户详细信息
        $user = null;
        if (isset($userInfo->uid)) {
            $user = \app\model\User::where('id', $userInfo->uid)->find();
        }

        return json([
            'code' => 0,
            'msg' => 'API正常工作',
            'data' => [
                'time' => date('Y-m-d H:i:s'),
                'request' => $request->all(),
                'student_count' => Student::where('is_delete', 0)->count(),
                'current_user' => $userInfo,
                'user_detail' => $user ? $user->toArray() : null
            ]
        ]);
    }

    /**
     * 处理 toggleAIOverlay 请求
     * 根据手机号查询学生，存在则更新，不存在则添加
     * 同时创建或更新择校报告
     *
     * @param Request $request
     * @return \support\Response
     */
    public function toggleAIOverlay(Request $request)
    {
        $data = $request->post();
       $userInfo =  User::where("id", $request->user->uid)->find();
       if($userInfo->all_report_times<=$userInfo->generated_report_times){
        return json([
            'code' => 400,
            'msg' => '没有更多的可生成报告次数',
            'data' => []
        ]);
       }
        // 记录请求参数
        Log::info('toggleAIOverlay 请求参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        // 验证必填字段
        if (empty($data['phone'])) {
            return json([
                'code' => 400,
                'msg' => '手机号不能为空',
                'data' => []
            ]);
        }

        // 开启事务
        \think\facade\Db::startTrans();
        try {
            if(isset($data['student_id'])){
                $student = Student::where('id', $data['student_id'])->where('is_delete', 0)->find();
                $isNewStudent = false;
            }
            if (!isset($data['student_id']) || !$student) {
                // 学生不存在，创建新学生
                $student = new Student;
                $isNewStudent = true;
            }

            // 更新或设置学生基本信息
            $student->name = $data['name'] ?? $student->name ?? '';
            // 性别：1-男，2-女，3-其他
            if (isset($data['sex']) && in_array($data['sex'], ['1', '2', '3'])) {
                $student->sex = $data['sex'];
            } else if (!$student->sex) {
                $student->sex = '1'; // 默认为男性
            }
            $student->phone = $data['phone'];
            $student->teacher_phone = $data['teacherPhone'] ?? $student->teacher_phone ?? '';

            // 确保本科院校信息不丢失
            if (!empty($data['undergraduateSchoolName'])) {
                $student->undergraduate_school_id = $data['undergraduateSchool'];
                $student->undergraduate_school_name = $data['undergraduateSchoolName'] ?? '';
            } else if ($student->undergraduate_school_id > 0) {
                // 保留原有数据
                $student->undergraduate_school_id = $student->undergraduate_school_id;
                $student->undergraduate_school_name = $student->undergraduate_school_name;
            }

            // 确保本科专业信息不丢失
            if (!empty($data['undergraduateMajorName'])) {
                $student->undergraduate_major_id = $data['undergraduateMajor'];
                $student->undergraduate_major_name = $data['undergraduateMajorName'] ?? '';
            } else if ($student->undergraduate_major_id > 0) {
                // 保留原有数据
                $student->undergraduate_major_id = $student->undergraduate_major_id;
                $student->undergraduate_major_name = $student->undergraduate_major_name;
            }

            // 确保目标专业信息不丢失
            if (is_array($data['targetMajor']) && count($data['targetMajor']) > 0) {
                // 检查是否包含*（采用一级学科）
                if(in_array("*", $data['targetMajor'])){    
                    $data['targetMajor'] = ["*"];
                    $data['majorCode'] = "*";
                    $data['targetMajorName'] = "采用一级学科";
                }

                $student->target_major_id = implode(',', $data['targetMajor']);

                // 处理目标专业名称
                $targetMajorNames = $data['targetMajorName'] ?? '';
                if (is_array($targetMajorNames)) {
                    $student->target_major_name = implode(',', $targetMajorNames);
                } else {
                    $student->target_major_name = $targetMajorNames;
                }

                // 处理专业代码
                $student->major_code = $data['majorCode'] ?? "";

                // 更新学科门类和一级学科
                $student->discipline_category_id = $data['disciplineCategory'] ?? null;
                $student->first_level_discipline_id = $data['firstLevelDiscipline'] ?? null;
            }

            // 处理布尔值字段
            if (isset($data['isPostgraduate'])) {
                $student->is_postgraduate = $data['isPostgraduate'] ? 1 : 0;
            }

            $student->exam_year = $data['examYear'] ?? $student->exam_year ?? '';

            // 处理是否跨专业字段
            if (isset($data['isMultiDisciplinary'])) {
                $student->is_multi_disciplinary = $data['isMultiDisciplinary'] ?? 0; // 确保跨专业字段类型一致
            }
            $student->educational_style = $data['educationalStyle'] ?? 0; // 确保培养方式字段类型一致

            // 设置为当前登录的管理员ID（如果是新学生且未设置）
            if ($isNewStudent || empty($student->admin_id)) {
                $student->admin_id = $request->user->uid ?? 0;
            }

            // 设置时间戳
            if ($isNewStudent) {
                $student->create_time = time();
            }
            $student->update_time = time();
            $student->save();

            $studentId = $student->id;
            // 处理学生详细信息
            $detail = StudentDetail::where('student_id', $studentId)->find();
            if (!$detail) {
                $detail = new StudentDetail;
                $detail->student_id = $studentId;
                $detail->create_time = time();
            }

            // 处理本科成绩
            if (isset($data['undergraduateTranscript'])) {
                $detail->undergraduate_transcript = is_array($data['undergraduateTranscript'])
                    ? json_encode($data['undergraduateTranscript'], JSON_UNESCAPED_UNICODE)
                    : $data['undergraduateTranscript'];
            }

            // 处理高考英语成绩
            if (isset($data['englishScore'])) {
                $detail->english_score = $data['englishScore'];
            }

            // 处理四级成绩 - 确保即使是空字符串也会更新
            if (array_key_exists('cet4', $data)) {
                $detail->cet4 = $data['cet4'];
                // 记录日志，用于调试
                Log::info('四级成绩: ' . ($data['cet4'] ?? '未设置'));
            }

            // 处理六级成绩 - 确保即使是空字符串也会更新
            if (array_key_exists('cet6', $data)) {
                $detail->cet6 = $data['cet6'];
                // 记录日志，用于调试
                Log::info('六级成绩: ' . ($data['cet6'] ?? '未设置'));
            }

            // 处理托福成绩
            if (isset($data['tofelScore'])) {
                $detail->tofel_score = $data['tofelScore'];
            }

            // 记录日志，用于调试
            Log::info('托福成绩: ' . ($data['tofelScore'] ?? '未设置'));

            // 处理雅思成绩
            if (isset($data['ieltsScore'])) {
                $detail->ielts_score = $data['ieltsScore'];
            }

            // 处理英语能力
            if (array_key_exists('englishAbility', $data)) {
                $detail->english_ability = $data['englishAbility'];
            }

            // 记录日志，用于调试
            Log::info('英语能力: ' . ($data['englishAbility'] ?? '未设置'));

            // 处理地区倾向
            if (isset($data['region'])) {
                $detail->target_region = is_array($data['region'])
                    ? json_encode($data['region'], JSON_UNESCAPED_UNICODE)
                    : $data['region'];

                // 记录日志，用于调试
                Log::info('地区倾向: ' . (is_array($data['region']) ? json_encode($data['region'], JSON_UNESCAPED_UNICODE) : $data['region']));
            }

            // 处理省份选择 - 直接存储汉字
            $detail->target_provinces = $data['targetProvinces'];

            // 处理梦校
            if (empty($data['targetSchoolName'])) {
               throw new \Exception('梦校不能为空');
            }
            $detail->target_school_id = $data['targetSchool'] ?? 0;
            $detail->target_school_name = $data['targetSchoolName'] ?? '';


            $majorCode = $data['majorCode'] ?? '';
            $majorCodes = [];


            
            // 处理多个专业代码（逗号分隔）
            $inputMajorCodes = is_array($majorCode) ? $majorCode : explode(',', $majorCode);
            $inputMajorCodes = array_filter(array_map('trim', $inputMajorCodes));
            Log::info('inputMajorCodes:'.implode(',', $inputMajorCodes)); // 记录日志，用于调试
            // 检查专业代码是否包含*，如果包含则表示采用一级学科
            if (in_array("*", $inputMajorCodes)) {
                $prefix = SecondMajor::getConnection()->getConfig('prefix');
                $majorCodes = SecondMajor::where('value', '=', function($query) use($data,$prefix){
                    $query->table( $prefix .'first_level_discipline_code')
                    ->where('id', $data['firstLevelDiscipline'])->field('yiji_value');
                })->column('major_code');

                Log::info('majors:'.implode(',', $majorCodes)); // 记录日志，用于调试

                $dreamSchoolInfo = SchoolInfo::where('school_name', $data['targetSchoolName'])
                                    ->whereIn('major_code', $majorCodes)
                                    ->find();
            } else {
                // 使用多个专业代码查询，优先查找第一个专业代码
                $dreamSchoolInfo = SchoolInfo::where('school_name', $data['targetSchoolName'])
                                    ->whereIn('major_code', $inputMajorCodes)
                                    ->find();
            }

            Log::info('dreamSchoolInfoSql: ' . db('ba_school_info')->getLastSql());
            if(empty($dreamSchoolInfo)){
                throw new \Exception('梦校没有该专业研究生招生计划');
            }

            //$detail->target_school_id = $dreamSchoolInfo['targetSchool'] ?? 0;

            // 处理院校层次
            if (isset($data['schoolLevel']) && is_array($data['schoolLevel'])) {
                $detail->school_level = implode(',', $data['schoolLevel']);
            }

            // 处理专业课参考书
            if (isset($data['referenceBooks'])) {
                $detail->reference_books = $data['referenceBooks'];
            }

            // 处理预估成绩
            if (isset($data['estimatedScores'])) {
                $detail->estimated_scores = is_array($data['estimatedScores'])
                    ? json_encode($data['estimatedScores'], JSON_UNESCAPED_UNICODE)
                    : $data['estimatedScores'];
            }

            // 处理政治分数
            if (isset($data['politics'])) {
                $detail->politics = $data['politics'];
            }

            // 字段映射说明：english_type=业务课一，math_type=业务课二，english_s=英语成绩
            $detail->english_type = $data['englishType'] ?? '';   // 业务课一
            $detail->math_type = $data['mathType'] ?? '';         // 业务课二
            $detail->english_s = $data['englishS'] ?? '';         // 英语成绩
            $detail->math_score = $data['mathScore'] ?? '';
            $detail->professional_score = $data['professionalScore'] ?? '';

            // 处理总分
            if (isset($data['totalScore'])) {
                $detail->total_score = $data['totalScore'];
            }

            // 处理个性化需求
            if (isset($data['personalNeeds'])) {
                $detail->personal_needs = $data['personalNeeds'];
            }

            // 处理薄弱模块
            if (isset($data['weakModules'])) {
                $detail->weak_modules = $data['weakModules'];
            }

            


            $detail->update_time = time();
            $detail->save();

            // 处理标签
            if (isset($data['tags']) && is_array($data['tags']) && !empty($data['tags'])) {
                // 先删除原有标签关联
                StudentTag::where('student_id', $studentId)->delete();

                $tagData = [];
                foreach ($data['tags'] as $tagId) {
                    // 确保标签ID是整数且大于0
                    $tagId = intval($tagId);
                    if ($tagId > 0) {
                        $tagData[] = [
                            'student_id' => $studentId,
                            'tag_id' => $tagId,
                            'create_time' => time()
                        ];
                    }
                }

                if (!empty($tagData)) {
                    // 记录日志，用于调试
                    Log::info('添加学生标签: ' . json_encode($tagData));
                    StudentTag::insertAll($tagData);
                }
            }
            // 创建或更新择校报告
            $report = new SchoolReport;
            $report->admin_id = $request->user->uid;
            $report->student_id = $studentId;
            $report->create_time = time();
            // 设置报告字段
            $report->target_major = $student->target_major_name ?? '';
            $report->major_code = $data["majorCode"] == "*" ? implode(',', $majorCodes) : $data["majorCode"];
            $report->target_major_code = $student->major_code;
            $report->dream_school = $detail->target_school_name ?? '';
            $report->dream_school_id = $detail->target_school ?? 0;
            $report->educational_style = $data['educationalStyle']?? 0;
            $report->undergraduate_major_name = $data['undergraduateMajorName'] ?? '';
            $report->undergraduate_school_name = $data['undergraduateSchoolName'] ?? '';
            $report->is_multi_disciplinary = $data['isMultiDisciplinary'] ?? 0;
            // 设置地区倾向
            if (isset($data['region'])) {
                $report->target_region = is_array($data['region'])
                    ? json_encode($data['region'], JSON_UNESCAPED_UNICODE)
                    : $data['region'];
            }

            // 设置省份选择
            if (isset($data['targetProvinces'])) {
                if (is_array($data['targetProvinces'])) {
                    // 将数组转换为逗号分隔的字符串
                    $report->target_provinces = implode(',', $data['targetProvinces']);
                } else {
                    $report->target_provinces = $data['targetProvinces'];
                }
            }

            // 设置院校层次
            $report->school_level = $detail->school_level ?? '';

            // 设置专业课参考书
            $report->reference_books = $detail->reference_books ?? '';

            // 设置预估分数 - 字段映射：english_type=业务课一，math_type=业务课二
            $report->politics_score = $detail->politics ?? 0;        // 政治
            $report->english_type = $detail->english_type ?? '';     // 业务课一
            $report->english_score = $detail->english_s ?? 0;        // 英语成绩
            $report->math_type = $detail->math_type ?? '';           // 业务课二
            $report->math_score = $detail->math_score ?? 0;
            $report->professional_score = $detail->professional_score ?? 0;
            $report->total_score = $detail->total_score ?? 0;

            // 设置个性化需求和薄弱模块
            $report->personal_needs = $detail->personal_needs ?? '';
            $report->weak_modules = $detail->weak_modules ?? '';

            //

            $report->save();
            $context = (new RemoteController())->buildAiContext($report->id);

            if($context === false){
                \think\facade\Db::rollback();
                return json([
                    'code' => 500,
                    'msg' => '构建学生基本信息失败',
                    'data' => null
                ]);
            }
            $report->context = $context;
            $report->save();
            \think\facade\Db::commit();

            return json([
                'code' => 0,
                'msg' => $isNewStudent ? '添加成功' : '更新成功',
                'data' => [
                    'id' => $studentId,
                    'report_id' => $report->id
                ]
            ]);
        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            Log::error('toggleAIOverlay 异常: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            return json([
                'code' => 500,
                'msg' => ($isNewStudent ?? false) ? '添加失败：' . $e->getMessage() : '更新失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新学生的服务老师
     * @param Request $request
     * @return \support\Response
     */
    public function updateTeacher(Request $request)
    {
        $data = $request->post();

        // 验证必填字段
        if (empty($data['studentIds']) || !is_array($data['studentIds'])) {
            return json([
                'code' => 400,
                'msg' => '学生ID不能为空',
                'data' => []
            ]);
        }

        if (empty($data['teacherId'])) {
            return json([
                'code' => 400,
                'msg' => '老师ID不能为空',
                'data' => []
            ]);
        }

        // 查找老师
        $teacher = \app\model\User::where('id', $data['teacherId'])->find();
        if (!$teacher) {
            return json([
                'code' => 404,
                'msg' => '老师不存在',
                'data' => []
            ]);
        }

        // 开启事务
        \think\facade\Db::startTrans();
        try {
            // 更新学生的服务老师
            foreach ($data['studentIds'] as $studentId) {
                $student = Student::where('id', $studentId)->where('is_delete', 0)->find();
                if ($student) {
                    $student->admin_id = $data['teacherId'];
                    $student->teacher_phone = $teacher['mobile'] ?? '';
                    $student->update_time = time();
                    $student->save();
                }
            }

            \think\facade\Db::commit();

            return json([
                'code' => 0,
                'msg' => '更新成功',
                'data' => []
            ]);
        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            return json([
                'code' => 500,
                'msg' => '更新失败：' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 获取学科门类列表
     * @return \support\Response
     */
    public function getDisciplineCategories()
    {
        try {
            $categories = Db::name('discipline_category')
                ->field('id, xueke_value as value, xueke_name as label')
                ->select();

            return json(['code' => 0, 'msg' => '获取成功', 'data' => $categories]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 根据学科门类ID获取一级学科列表
     * @param Request $request
     * @return \support\Response
     */
    public function getFirstLevelDisciplines(Request $request)
    {
        $categoryId = $request->get('category_id');
        if (!$categoryId) {
            return json(['code' => 1, 'msg' => '学科门类ID不能为空']);
        }

        try {
            $disciplines = Db::name('first_level_discipline_code')
                ->where('xueke_id', $categoryId)
                ->field('id, yiji_value as value, yiji_name as label')
                ->select();

            return json(['code' => 0, 'msg' => '获取成功', 'data' => $disciplines]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 根据一级学科ID获取二级学科（目标专业）列表
     * @param Request $request
     * @return \support\Response
     */
    public function getSecondLevelDisciplines(Request $request)
    {
        $firstLevelId = $request->get('first_level_id');
        if (!$firstLevelId) {
            return json(['code' => 1, 'msg' => '一级学科ID不能为空']);
        }

        try {
            $majors = Db::name('erji_xueke')
                ->where('yiji_id', $firstLevelId)
                ->where('is_delete', 0)
                ->field('id, erji_value as value, erji_name as label, major_code')
                ->select();
            $majors[] = [
                'id'=>'*', 
                'value'=>'*',
                'label'=>'(*)采用一级学科', 
                'major_code'=>'*'
            ];
            return json(['code' => 0, 'msg' => '获取成功', 'data' => $majors]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }
}
