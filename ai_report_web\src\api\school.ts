import request from '@/utils/request'
import { ApiResponse, School, Major, SchoolInfo } from '@/types'

/**
 * 搜索学校
 * @param keyword - 搜索关键词
 * @returns Promise
 */
export function searchSchool(keyword: string): Promise<ApiResponse<School[]>> {
  return request({
    url: '/school/search',
    method: 'get',
    params: { keyword }
  })
}

/**
 * 搜索专业
 * @param schoolId - 学校ID
 * @param majorName - 专业名称关键词
 * @param page - 页码，可选，默认为1
 * @returns Promise
 */
export function searchMajor(schoolId: number | string, majorName: string, page?: number): Promise<ApiResponse<Major[]>> {
  const params: any = {
    school_id: schoolId,
    major_name: majorName
  };
  
  if (page) {
    params.page = page;
  }
  
  return request({
    url: '/school/major/search',
    method: 'get',
    params
  })
}

/**
 * 搜索目标专业
 * @param keyword - 搜索关键词
 * @returns Promise
 */
export function searchTargetMajor(keyword: string): Promise<ApiResponse<Major[]>> {
  return request({
    url: '/major/search',
    method: 'get',
    params: { keyword }
  })
}

/**
 * 获取考研年份列表
 * @returns Promise
 */
export function getExamYears(): Promise<ApiResponse<{value: string, label: string}[]>> {
  return request({
    url: '/exam/years',
    method: 'get'
  })
}

/**
 * 根据预估分数和专业信息获取院校数据
 * @param score - 预估分数
 * @param majorCode - 专业代码
 * @param majorName - 专业名称
 * @param provinces - 省份，多个省份用逗号分隔
 * @param limit - 返回数量限制，默认20条
 * @returns Promise
 */
export function getSchoolsByScore(
  score: number,
  majorCode?: string,
  majorName?: string,
  provinces?: string,
  limit?: number
): Promise<ApiResponse<SchoolInfo[]>> {
  return request({
    url: '/school/score',
    method: 'get',
    params: {
      score,
      major_code: majorCode,
      major_name: majorName,
      provinces,
      limit
    }
  })
}

/**
 * 获取国家线数据
 * @param firstLevelDiscipline - 一级学科ID
 * @returns Promise
 */
export function getNationalLineData(firstLevelDiscipline: string): Promise<ApiResponse<any>> {
  return request({
    url: '/school/national-line',
    method: 'get',
    params: { firstLevelDiscipline: firstLevelDiscipline }
  })
}

/**
 * 获取学校信息列表（用于添加院校功能）
 * @param params - 查询参数
 * @returns Promise
 */
export function getSchoolInfoList(params: {
  page?: number;
  limit?: number;
  keyword?: string;
  report_id?: string | number;
}): Promise<ApiResponse<{
  list: SchoolInfo[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}>> {
  return request({
    url: '/school/info/list',
    method: 'get',
    params
  })
}

/**
 * 获取院校详细信息
 * @param params - 查询参数
 * @returns Promise
 */
export function getSchoolDetailInfo(params: {
  school_id: string | number;
  year?: string;
}): Promise<ApiResponse<{
  admission_list: any[];
  retest_list: any[];
  school_basic_info: any;
  school_info: any;
}>> {
  return request({
    url: '/school/detail/info',
    method: 'get',
    params
  })
}

export default {
  searchSchool,
  searchMajor,
  searchTargetMajor,
  getExamYears,
  getSchoolsByScore,
  getNationalLineData,
  getSchoolInfoList,
  getSchoolDetailInfo
}
