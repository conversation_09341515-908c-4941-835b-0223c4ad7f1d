import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import * as path from "path";
import postcssPxToViewport from "postcss-px-to-viewport";
// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      dts: 'src/auto-imports.d.ts',
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      dts: 'src/components.d.ts',
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve("./src"),
    },
  },
  server: {
    proxy: {
      "/api/remote/get_study_plan": {
        target: 'http://127.0.0.1:8788',
        changeOrigin: true,
      },
      "/api/remote/regenerate_field": {
        target: 'http://127.0.0.1:8789',
        changeOrigin: true,
      },
      "/api/remote/stream_ai_recommendation": {
        target: 'http://127.0.0.1:8790',
        changeOrigin: true,
      },
      '/api': {
        //target: 'https://report.yanqukaoyan.com/',
        target: 'http://127.0.0.1:8787',
        changeOrigin: true,
      }
    }
  },
  css: {
    postcss: {
      plugins: [
        {
          postcssPlugin: 'targeted-px2vw',
          Once(root, { result }) {

            const filePath = result.opts.from
            // 只处理目标文件
            const isTargetFile = filePath.includes(
                '/src/views/report/show_report.vue'
            )
            if (!isTargetFile) return
            const processor = postcssPxToViewport({
              viewportWidth: 750,
              unitToConvert: "px",
              unitPrecision: 5,
              propList: ["*"],
              viewportUnit: "vw",
              fontViewportUnit: "vw",
              selectorBlackList: [],
              minPixelValue: 1,
              mediaQuery: false,
              replace: true
            })
            return processor(root, result)
          }
        }
      ],
    },
  },
});
