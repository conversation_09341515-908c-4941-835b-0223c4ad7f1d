* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  height: auto;
  width: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  color: #333;
 background-color: #f0f2f5;
  /* background-color: #fff; */
  overflow: hidden;
}

#app {
  height: auto;
  width: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 10px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

/* Element-Plus 自定义样式 */
.el-menu {
  border-right: none !important;
}

.el-card {
  border-radius: 4px;
  border: none;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08) !important;
}

.el-card__header {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: bold;
}

.el-card__body {
  padding: 20px;
}

/* 常用工具类 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.full-height {
  height: 100%;
}

/* Element-Plus 表单组件 placeholder 居中样式 */
.el-input__inner::placeholder {
  text-align: center !important;
}

.el-textarea__inner::placeholder {
  text-align: center !important;
}

.el-select .el-input__inner::placeholder {
  text-align: center !important;
}

.el-cascader .el-input__inner::placeholder {
  text-align: center !important;
}

.el-date-editor .el-input__inner::placeholder {
  text-align: center !important;
}

.el-autocomplete .el-input__inner::placeholder {
  text-align: center !important;
}

.el-input-number .el-input__inner::placeholder {
  text-align: center !important;
}

.el-select__selected-item .el-select__input::placeholder {
  text-align: center !important;
}
.el-select__selected-item.el-select__placeholder.is-transparent{
  text-align: center !important;
}

