<?php
use Webman\Route;

// 学校和专业相关接口
Route::group("/api", function () {
    // 学校搜索
    Route::get('/school/search', [app\controller\SchoolController::class, 'search']);
    Route::get('/school/major/search', [app\controller\SchoolController::class, 'searchSchoolMajor']);

    // 专业搜索
    Route::get('/major/search', [app\controller\SchoolController::class, 'searchMajor']);

    // 考试年份
    Route::get('/exam/years', [app\controller\SchoolController::class, 'getExamYears']);

    // 获取学校信息列表（用于添加院校功能）
    Route::get('/school/info/list', [app\controller\SchoolController::class, 'getSchoolInfoList']);
})->middleware([app\middleware\JwtMiddleware::class]);