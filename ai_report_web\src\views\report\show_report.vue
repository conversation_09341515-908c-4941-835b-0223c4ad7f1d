<template>
  <div class="home-bg">
    <div class="content-wrapper"  v-loading.fullscreen.lock="is_show">
      <!-- 示例：顶部Logo和标题 -->
      <div class="logo-wrapper">
        <img class="logo" src="@/assets/mobile/logo.png" alt="logo" />
      </div>

      <h1 class="title">研趣考研院校分析报告</h1>
      <!-- 这里根据长图内容分块布局，以下为示例 -->
      <div class="section">姓名：{{ studentInfo?.name}}</div>
      <!-- 继续添加其它内容块，使用Element Plus组件和自定义样式 -->
    </div>
    <div class="card">
      <!-- 第一部分：个人基础信息 -->
      <div class="info-section">
        <div class="info-section-title-bg">
          <img src="@/assets/mobile/person_base1.png" mode="heightFix" alt="第一部分标题背景"  class="title-bg-img"/>
        </div>
  
        <div class="info-block">
          <div>
            <div class="step-num-tag">
              <span>01</span>
              <div class="tag-text">个人基础信息</div>
            </div>
            <div class="info-block-content">
              <div class="info-block-item">
                <span class="info-block-item-title">姓名:</span>
                <span class="info-block-item-content">{{studentInfo?.name}}</span>
              </div>
              <div class="info-block-item">
                <span class="info-block-item-title">性别:</span>
                <span class="info-block-item-content">{{studentInfo?.sexText}}</span>
              </div>
              <div class="info-block-item">
                <span class="info-block-item-title">本科院校:</span>
                <span class="info-block-item-content">{{studentInfo?.undergraduateSchoolName}}</span>
              </div>

              <div class="info-block-item">
                <span class="info-block-item-title">本科专业:</span>
                <span class="info-block-item-content">{{studentInfo?.undergraduateMajorName}}</span>
              </div>
              <div class="info-block-item">
                <span class="info-block-item-title">联系方式:</span>
                <span class="info-block-item-content">{{studentInfo?.phone}}</span>
              </div>
              <div class="info-block-item">
                <span class="info-block-item-title">考研届数:</span>
                <span class="info-block-item-content">{{studentInfo?.examYear}}</span>
              </div>
              <div class="info-block-item">
                <span class="info-block-item-title">跨专业:</span>
                <span class="info-block-item-content">{{studentInfo?.isCrossMajorText}}</span>
              </div>
              <div class="info-block-item">
                <span class="info-block-item-title">培养方式:</span>
                <span class="info-block-item-content">{{studentInfo?.educationalStyleText}}</span>
              </div>
              <div class="info-block-item  major-block">
                <span class="info-block-item-title">专业代码:</span>
                <span class="info-block-item-content">{{reportInfo?.major_code}}</span>
              </div>
              <div class="info-block-item major-block">
                <span class="info-block-item-title">目标专业:</span>
                <span class="info-block-item-content">{{reportInfo?.target_major}}</span>
              </div>
            </div>

          </div>
          <div>
            <div class="step-num-tag">
              <span>02</span>
              <div class="tag-text">本科成绩情况</div>
            </div>
            <div class="info-block-content">
              <div class="info-block-item" v-if="studentInfo?.undergraduateTranscript && studentInfo?.undergraduateTranscript.length > 0" :key="sindex" v-for="(sitem,sindex) in  studentInfo.undergraduateTranscript">
                <span class="info-block-item-title">{{sitem.title}}</span>
                <span class="info-block-item-content">{{sitem.score}}</span>
              </div>
            </div>
          </div>
          <div>
            <div class="step-num-tag">
              <span>03</span>
              <div class="tag-text">英语基础</div>
            </div>
            <div class="info-block-content">
              <div class="info-block-item">
                <span class="info-block-item-title">高考英语成绩:</span>
                <span class="info-block-item-content">{{studentInfo?.englishScore}}</span>
              </div>
              <div class="info-block-item">
                <span class="info-block-item-title">大学四级:</span>
                <span class="info-block-item-content">{{studentInfo?.cet4}}</span>
              </div>
              <div class="info-block-item">
                <span class="info-block-item-title">大学六级:</span>
                <span class="info-block-item-content">{{studentInfo?.cet6}}</span>
              </div>
              <div class="info-block-item">
                <span class="info-block-item-title">托福:</span>
                <span class="info-block-item-content">{{studentInfo?.tofelScore?studentInfo?.tofelScore:''}}</span>
              </div>
              <div class="info-block-item">
                <span class="info-block-item-title">雅思:</span>
                <span class="info-block-item-content">{{studentInfo?.englishScores?.ielts}}</span>
              </div>
              <div class="info-block-item">
                <span class="info-block-item-title">熟练语法:</span>
                <span class="info-block-item-content">{{studentInfo?.englishScores?.grammarProficiency ? '是' : '否'}}</span>
              </div>
            </div>
          </div>
          <div>
            <div class="step-num-tag">
              <span>04</span>
              <div class="tag-text">目标院校倾向</div>
            </div>
            <div class="info-other-block-content">
              <div class="info-other-block-item  info_area">
                <span class="info-other-block-item-title">地区倾向:</span>
                <span class="info-other-block-item-content">{{reportInfo?.region_preference}}</span>
              </div>
              <div class="info-other-block-item">
                <span class="info-other-block-item-title">省份选择:</span>
                <span class="info-other-block-item-content">{{reportInfo?.province_selection}}</span>
              </div>
            </div>
            <div class="info-other-block-content">
              <div class="info-other-block-item  info_area">
                <span class="info-other-block-item-title">梦校:</span>
                <span class="info-other-block-item-content">{{reportInfo?.dream_school}}</span>
              </div>
              <div class="info-other-block-item">
                <span class="info-other-block-item-title">院校层次:</span>
                <span class="info-other-block-item-content">{{reportInfo?.school_level}}</span>
              </div>
            </div>
             <div class="info-other-block-content">
              <div class="info-other-block-item" style="flex-basis: 100%;">
                <span class="info-other-block-item-title">专业课制定参考书:</span>
                <span class="info-other-block-item-content">{{reportInfo?.reference_books}}</span>
              </div>
            </div>
          </div>
          <!-- 新增表格部分 -->
          <div class="info-section">
            <div class="info-block">
              <div>
                <div class="step-num-tag">
                  <span>06</span>
                  <div class="tag-text">考试成绩预估</div>
                </div>
                <div class="score-table">
                  <div class="table-header">
                    <div class="th-cell cell-color">政治</div>
                    <div class="th-cell cell-color">英语</div>
                    <div class="th-cell cell-color">业务课一</div>
                    <div class="th-cell cell-color">业务课二</div>
                    <div class="th-cell cell-color">总分</div>
                  </div>
                  <div class="table-row-score">
                    <div class="td-cell">
                      {{ studentInfo?.politics }}
                    </div>
                    <div class="td-cell">
                      {{ studentInfo?.englishS }}
                    </div>
                    <div class="td-cell">
                      {{ studentInfo?.englishType }}
                    </div>
                    <div class="td-cell">
                      {{ studentInfo?.mathType }}

                    </div>
                    <div class="td-cell">
                      {{ studentInfo?.totalScore }}
                    </div>
                  </div>
                </div>
                <div class="personal-demands">
                  <div class="demands-label">
                    <span class="demands-label-title">个性化需求:</span>
                    <span>{{reportInfo?.personal_needs}}</span>
                  </div>
                </div>

                <div class="expertise-advice">
                  <div class="advice-label">
                    <span class="advice-label-title">薄弱模块:</span>
                    <span>{{reportInfo?.weak_modules}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
       
        <div class="info-section-title-bg">
          <img src="@/assets/mobile/professional_analysis.png" alt="第一部分标题背景" class="title-bg-img"/>
        </div>
        <div class="step-content">
          <div class="subject_info">
            <span>{{chartAtext}}</span>
          </div>
          <div ref="echartsARef" class="echarts-box"></div>
          <div class="subject_info">
            <span>{{chartBtext}}</span>
          </div>
          <div ref="echartsBRef" class="echarts-box"></div>
        </div>

        <div class="info-section-title-bg">
          <img src="@/assets/mobile/school_overview.png" alt="第一部分标题背景" class="title-bg-img"/>
        </div>
        <div class="step-content">
          <div class="school-table-container">
            <!-- 表格头部 -->
            <div class="school-table-header">
              <div class="header-cell">序号</div>
              <div class="header-cell">院校名称</div>
              <div class="header-cell">所在地区</div>
              <div class="header-cell">所在省市</div>
              <div class="header-cell">学院</div>
              <div class="header-cell">专业</div>
              <div class="header-cell">专业代码</div>
              <div class="header-cell">最低分</div>
              <div class="header-cell">最低分分差</div>
            </div>

            <!-- 表格内容 -->
            <div class="school-table-body">
              <div
                class="table-row"
                v-for="(item, index) in reportData?.school_list || []"
                :key="index"
              >
                <div class="body-cell">{{ item.id }}</div>
                <div class="body-cell school-name">
                  <div>{{ item.school_name }}</div>
                  <div class="school-tags">
                    <span class="tag tag-985" v-if="item.tag_985">985</span>
                    <span class="tag tag-211" v-if="item.tag_211">211</span>
                    <span class="tag tag-double" v-if="item.tag_double"
                      >双一流</span
                    >
                  </div>
                </div>
                <div class="body-cell">{{ item.region }}</div>
                <div class="body-cell">{{ item.city }}</div>
                <div class="body-cell">{{ item.college }}</div>
                <div class="body-cell">{{ item.major_name }}</div>
                <div class="body-cell">{{ item.major_code }}</div>
                <div class="body-cell">{{ item.min_score }}</div>
                <div class="body-cell">{{ item.score_diff }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="info-section-title-bg">
          <img src="@/assets/mobile/school_analysis.png" alt="第一部分标题背景" class="title-bg-img"/>
        </div>
        <div class="step-num-tag school-step-num-tag">
          <span>01</span>
          <div class="tag-text">院校推荐</div>
        </div>
        <div
          class="step-content"
          v-for="(school, index) in reportData?.recommend_list || []"
          :key="index"
        >
          <!-- 院校详情卡片 -->
          <div class="school-detail-card">
            <div class="school-header">
              <div class="school-logo">
                <img
                  :src="
                    'https://' + school?.school_info?.logo ||
                    '@/assets/images/school.png'
                  "
                  alt="学校logo"
                />
              </div>
              <div class="school-info">
                <div class="school-title ">
                  <h2>{{ school?.school_name || "" }}</h2>
                  <span class="school-location">{{
                    school?.province_city || ""
                  }}</span>
                </div>
                <div class="school-tags-row">
                  <span
                    class="tag tag_other tag-double"
                    v-if="school?.school_info?.is_dual_class"
                    >双一流</span
                  >
                  <span
                    class="tag tag_other tag-985"
                    v-if="school?.school_info?.tag_985"
                    >985</span
                  >
                  <span
                    class="tag tag_other tag-211"
                    v-if="school?.school_info?.tag_211"
                    >211</span
                  >
                </div>
                
              </div>
            </div>
            <h3 class="section-title">院校情况</h3>
            <div class=" reexam-container">
              <div class="reexam-card">
                <div class="reexam-header">
                  <img src="@/assets/images/tag.png" alt="标签图标" />
                  <div class="reexam-title">总成绩计算公式</div>

                </div>
                <div class="reexam-content">
                  <div>
                    {{ school?.basic_info?.score_formula }}
                  </div>

                </div>
              </div>

              <div class="reexam-card">
                <div class="reexam-header">
                  <img src="@/assets/images/tag.png" alt="标签图标" />
                  <div class="reexam-title">学制</div>

                </div>
                <div class="reexam-content">
                  <div>
                    {{ school?.basic_info?.study_years }}
                  </div>

                </div>
              </div>

              <div class="reexam-card">
                <div class="reexam-header">
                  <img src="@/assets/images/tag.png" alt="标签图标" />
                  <div class="reexam-title">学费</div>

                </div>
                <div class="reexam-content last-reexam-content">
                  <div>
                    {{ school?.basic_info?.tuition_fee }}
                  </div>
                </div>
              </div>
            </div>
            <h3 class="section-title section-title-font">初试模块</h3>
            <div class="reexam-container">
              <!--              <div class="detail-item">-->
              <!-- <div class="item-icon">
                <img src="@/assets/images/tag.png" alt="标签图标" />
              </div> -->
              <!-- <div class="item-content">
                <h4>总成绩计算公式</h4>
                <p>
                  总成绩 = (初试成绩 + 5) × 初试权重 + 复试成绩 × 复试权重
                </p>
              </div> -->
              <!--              </div>-->

              <div class="reexam-card">
                <div class="reexam-header">
                  <img src="@/assets/images/tag.png" alt="标签图标" />
                  <div class="reexam-title">初试考试科目</div>

                </div>
                <div class="reexam-content">
                  <div>
                    {{ school?.basic_info?.exam_range }}
                  </div>

                </div>
              </div>

              <div class="reexam-card">
                <div class="reexam-header">
                  <img src="@/assets/images/tag.png" alt="标签图标" />
                  <div class="reexam-title">考试专业课参考书</div>

                </div>
                <div class="reexam-content last-reexam-content">
                  <div>
                    {{ school?.basic_info?.reference_books }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 第六步：招生情况 -->

            <div class="step-content-zs">
              <!-- 招生情况标题 -->
              <div
                class="admission-title"
                v-if="
                  school?.admission_data && school.admission_data.length > 0
                "
              >
                招生情况（{{ school.admission_data[0]?.year }}年）
              </div>

              <!-- 招生计划表格 -->
              <table class="admission-table">
                <thead>
                  <tr>
                    <th>招生计划</th>
                    <th>一志愿人数</th>
                    <th>总录取数</th>
                    <th>一志愿录取比</th>
                    <th>调剂人数</th>
                    <th>最高分</th>
                    <th>最低分</th>
                    <th>平均分</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in school?.admission_data" :key="index">
                    <td>{{ item.planCount }}</td>
                    <td>{{ item.examCount }}</td>
                    <td>{{ item.admitCount }}</td>
                    <td>{{ item.ratio }}</td>
                    <td>{{ item.studentCount }}</td>
                    <td>{{ item.highestScore }}</td>
                    <td>{{ item.lowestScore }}</td>
                    <td>{{ item.averageScore }}</td>
                  </tr>
                </tbody>
              </table>

              <!-- 一志愿考试名单标题 -->
              <div class="admission-title">一志愿考试名单</div>

              <!-- 一志愿考试名单表格 -->
              <table class="admission-table">
                <thead>
                  <tr>
                    <th>编号</th>
                    <th>学生姓名</th>
                    <th>政治</th>
                    <th>英语</th>
                    <th>专业课一</th>
                    <th>专业课二</th>
                    <th>初试成绩</th>
                    <th>是否一志愿</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(item, index) in school?.current_year_retest_list
                      ?.list || []"
                    :key="index"
                  >
                    <td>{{ index + 1 }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.politics_score }}</td>
                    <td>{{ item.english_score }}</td>
                    <td>{{ item.major1_score }}</td>
                    <td>{{ item.major2_score }}</td>
                    <td>{{ item.initial_score }}</td>
                    <td>{{ item.admission_status }}</td>
                  </tr>
                </tbody>
              </table>

              <!-- 复试模块标题 -->
              <div class="admission-title">复试模块</div>

              <!-- 复试模块内容 -->
              <div class="reexam-container">
                <div class="reexam-card">
                  <div class="reexam-header">
                    <img src="@/assets/images/tag.png" alt="标签图标" />
                    <div class="reexam-title">复试内容</div>
                  </div>
                  <div class="reexam-content">
                    {{ school?.basic_info?.retest_content }}
                  </div>
                </div>

<!--                <div class="reexam-card">-->
<!--                  <div class="reexam-header">-->
<!--                    <img src="@/assets/images/tag.png" alt="标签图标" />-->
<!--                    <div class="reexam-title">复试录取比例</div>-->
<!--                  </div>-->
<!--                  <div class="reexam-content">-->
<!--                    <p-->
<!--                      v-if="-->
<!--                        school?.current_year_retest_list?.count &&-->
<!--                        school?.current_year_admission_list?.count-->
<!--                      "-->
<!--                    >-->
<!--                      录取比例-->
<!--                      {{-->
<!--                        school.current_year_retest_list.count +-->
<!--                        ":" +-->
<!--                        school.current_year_admission_list.count-->
<!--                      }}-->
<!--                    </p>-->
<!--                    <p v-else>暂无数据</p>-->
<!--                  </div>-->
<!--                </div>-->
              </div>

              <!-- 报录取名单标题 -->
              <div class="admission-title">拟录取名单</div>

              <!-- 报录取名单表格 -->
              <table class="admission-table">
                <thead>
                  <tr>
                    <th>编号</th>
                    <th>学生姓名</th>
                    <th>初试成绩</th>
                    <th>复试成绩</th>
                    <th>两项总成绩</th>
                    <th>一志愿学校</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(item, index) in school
                      ?.current_year_admission_list?.list || []"
                    :key="index"
                  >
                    <td>{{ index + 1 }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.initial_score }}</td>
                    <td>{{ item.retest_score }}</td>
                    <td>{{ item.total_score }}</td>
                    <td>{{ item.first_choice_school }}</td>
                  </tr>
                </tbody>
              </table>

              <!-- 综合建议标题 -->
              <div class="admission-title">综合建议</div>

              <!-- 综合建议内容 -->
              <div class="reexam-container">
                <div class="reexam-card">
                  <div class="reexam-header">
                    <img src="@/assets/images/tag.png" alt="标签图标" />
                    <div class="reexam-title">竞争难度分析</div>
                    
                  </div>
                  <div class="reexam-content">
                    <div>
                      {{ school.difficulty_analysis }}
                    </div>
                  
                  </div>
                </div>

                <div class="reexam-card">
                  <div class="reexam-header">
                    <img src="@/assets/images/tag.png" alt="标签图标" />
                    <div class="reexam-title">备考目标建议</div>
                   
                  </div>
                  <div class="reexam-content last-reexam-content">
                    <div>
                      {{ school.suggest }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="step-num-tag">
          <span>03</span>
          <div class="tag-text">推荐综合性价比高的院校</div>
        </div>
      <!-- 推荐综合性价比高的院校内容 -->
        <div class="recommend-school-container">
          <div class="recommend-school-card">
            <div class="recommend-school-header">
              <div class="recommend-icon">
                <img src="@/assets/images/tag.png" alt="标签图标" />
              </div>
              <div class="recommend-title">推荐原因</div>
            </div>
            <div class="recommend-school-content">
              {{ reportData?.high_recommend_list?.reason || "暂无推荐信息" }}
            </div>
          </div>
        </div>
         <div class="info-section-title-bg">
          <img src="@/assets/mobile/five_png.png" alt="第一部分标题背景" class="title-bg-img"/>
        </div>
        <div class="step-num-tag">
          <span>01</span>
          <div class="tag-text">薄弱模块分析</div>
        </div>
      <!-- 推荐综合性价比高的院校内容 -->
        <div class="recommend-school-container">
          <div class="recommend-school-card" v-if="studyPlanData?.weakModuleAnalysis.length > 0"
               v-for="(weakitem,weakindex) in  studyPlanData.weakModuleAnalysis"
               :key="weakindex"
          >
            <div class="recommend-school-header">
              <div class="recommend-icon">
                <img src="@/assets/images/tag.png" alt="标签图标" />
              </div>
              <div class="recommend-title">科目</div>
            </div>
            <div class="recommend-school-content">
              {{ weakitem?.subject || "英语一" }}
            </div>
            <div class="recommend-school-header">
              <div class="recommend-icon">
                <img src="@/assets/images/tag.png" alt="标签图标" />
              </div>
              <div class="recommend-title">问题分析</div>
            </div>
            <div class="recommend-school-content">
              {{ weakitem?.problemAnalysis || "考研英语难度与要求考研英语（一）难度较高，重点考查 阅读理解（40分）、写作（30分） 和 长难句分析能力，词汇量要求约5500词。USTC电子信息专业虽为工科，但英语单科线通常在 50分左右（需参考往年分数线），且总分要求较高，英语短板可能拖累整体成绩。你的薄弱节可能包括词汇量不足：高频词、学术词汇（如科技类文章常见词）不熟悉。长难句解析困难：影响阅读速度和答题准确率。写作模板化：缺乏灵活应对图表作文、议论文的能力。时管理差：阅读或翻译耗时过长，导致完形填空等题型失分。电子信息专业的特殊性复试可" }}
            </div>
            <div class="recommend-school-header">
              <div class="recommend-icon">
                <img src="@/assets/images/tag.png" alt="标签图标" />
              </div>
              <div class="recommend-title">解决方案</div>
            </div>
            <div class="recommend-school-content">
              {{ weakitem?.problemAnalysis || "暂无推荐信息" }}
            </div>
          </div>
        </div>
        <div class="info-section">
            <div class="info-block">      
              <div class="step-num-tag">
                <span>02</span>
                <div class="tag-text">目标分数</div>
              </div>
              <div class="score-table target_score-table">
                <div class="table-header">
                  <div class="th-cell cell-color">政治</div>
                  <div class="th-cell cell-color">英语</div>
                  <div class="th-cell cell-color">业务课一</div>
                  <div class="th-cell cell-color">业务课二</div>
                  <div class="th-cell cell-color">总分</div>
                </div>
                <div class="table-row-score">
                  <div class="td-cell">
                    {{ targetScore?.politics }}
                  </div>
                  <div class="td-cell">
                    {{ targetScore?.english }}
                  </div>
                  <div class="td-cell">
                    {{ targetScore?.business1 }}
                  </div>
                  <div class="td-cell">
                    {{ targetScore?.business2 }}

                  </div>
                  <div class="td-cell">
                    {{targetScore?.total }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="info-section">
            <div class="info-block">      
              <div class="step-num-tag">
                <span>03</span>
                <div class="tag-text">初试各科学习规划</div>
              </div>
              <div v-if="studyPlanData?.studyPlanning.stages.length > 0"
                v-for="(studyitem,studyindex) in  studyPlanData?.studyPlanning.stages" :key="studyindex"
              >
                <div class="first_stage">{{studyitem.title}}</div>
                <div class="first_stage_content"> 
                  <div v-if="studyitem.modules.length >0" v-for="(moduleitem,moduleindex) in  studyitem.modules" :key="moduleindex">
                    <div class="stage_module">{{moduleitem?.name}}</div>
                    <div class="recommend-school-header">
                      <div class="recommend-icon">
                        <img src="@/assets/images/tag.png" alt="标签图标" />
                      </div>
                      <div class="stage_module-title">学习内容</div>
                    </div>
                    <div class="recommend-school-content">
                      {{ moduleitem?.studyContent || "暂无" }}
                    </div>
                    <div class="recommend-school-header">
                      <div class="recommend-icon">
                        <img src="@/assets/images/tag.png" alt="标签图标" />
                      </div>
                      <div class="recommend-title">学习方法</div>
                    </div>
                    <div class="recommend-school-content">
                      {{ moduleitem?.studyMethod || "暂无" }}
                    </div>
                    <div class="recommend-school-header">
                      <div class="recommend-icon">
                        <img src="@/assets/images/tag.png" alt="标签图标" />
                      </div>
                      <div class="recommend-title">资料建议</div>
                    </div>
                    <div class="recommend-school-content">
                      {{ moduleitem?.studyMaterials || "暂无" }}
                    </div>
                    <div class="recommend-school-header">
                      <div class="recommend-icon">
                        <img src="@/assets/images/tag.png" alt="标签图标" />
                      </div>
                      <div class="recommend-title">要点提醒</div>
                    </div>
                    <div class="recommend-school-content">
                      {{ moduleitem?.studyReminder || "暂无" }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="report_suggest">
            <div class="report_suggest_title">综合建议</div>
            <div class="report_suggest_content">{{studyPlanData?.comprehensiveAdvice || '暂无'}}</div>
          </div>
        <!-- 后续的 英语基础，目标院校倾向，考研成绩预估等可以照此结构添加 -->

      </div>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { ref,onMounted,Ref} from 'vue';
import * as echarts from "echarts";
import type { ECharts, EChartsOption } from "echarts";
import {getStudyPlanFromDatabaseEdit, reportByCode,getTargetScoreDetail} from "@/api/report";
import { useRouter } from 'vue-router';
import {
  getNationalLineData,
} from "@/api/school";
import {ElMessage} from "element-plus";

const route = useRouter();

const is_show= ref(true)

interface StudentInfo {
  name: string;
  sexText: string;
  undergraduateSchoolName?: string;
  undergraduateMajorName?: string;
  isCrossMajorText?:string;
  targetMajor: string;
  majorCode: string;
  contact: string;
  educationalStyleText?: string;
  undergraduateTranscript?:any;
  englishScore?: number;
  cet4?:  number;
  cet6?: number;
  tofelScore?:  number;
  englishScores?:any;
  targetRegion?:any[];
  targetProvinces?:any[];
  firstLevelSubject?: string; 
  targetMajorName?: string;
  politics?: string;
  englishS?: string;
  mathScore?: number;
  professionalScore?: number;
  phone?:string;
  examYear?:string;
  targetSchoolName?: string;
  schoolLevel:  string;
  referenceBooks: string;
  englishType?: string;
  mathType?: string;
  personalNeeds?: string;
  totalScore?: number;
  // ... 其他信息
}

interface Report {
  dream_school?:string;
  educational_style: string;
  english_score: string;
  english_type: string;
  major_code?: string;
  math_score?:any;
  math_type?: number;
  personal_needs?:  number;
  politics_score?: number;
  professional_score?:  number;
  province_selection?:any;
  reference_books?:any[];
  region_preference?:any[];
  school_level?: string;
  target_major?: string;
  total_score?: string;
  undergraduate_major_name?: string;
  undergraduate_school_name?: number;
  weak_modules?: number;
}
// 编辑相关状态
const studentInfo = ref<StudentInfo>();
const reportInfo = ref<Report>();

interface ReportData {
  school?: any;
  high_recommend_list?: {
    reason?: string;
  } ;
  national_line_list?: NationalLineData[];
  recommend_list?: any[];
  reexam_list?: any[];
  school_list?: any[];
  firstLevelSubject:string;

}

// 薄弱模块分析接口
interface WeakModuleAnalysis {
  id?: number; // 数据库ID，用于编辑
  subject: string;
  problemAnalysis: string;
  solutions: string;
}

// 学习阶段数据接口
interface StudyStage {
  id: string;
  title: string; // 阶段标题
  modules: StudyModule[]; // 该阶段的模块数组
}


// 学习模块数据接口
interface StudyModule {
  id: string;
  dbId?: number; // 数据库ID，用于编辑
  name: string; // 科目名称
  studyContent: string; // 学习内容
  studyMethod: string; // 学习方法
  studyMaterials: string; // 资料推荐
  studyReminder: string; // 要点提醒
}
// 学习规划数据接口
interface StudyPlanning {
  title: string; // 主标题
  stages: StudyStage[]; // 阶段数组
}


// 完整的学习计划数据接口
interface StudyPlanData {
  weakModuleAnalysis: WeakModuleAnalysis[];
  studyPlanning: StudyPlanning;
  comprehensiveAdvice: string;
  comprehensiveAdviceId?: number; // 综合建议的数据库ID
}


const reportData = ref<ReportData>();

interface NationalLineData {
  subject_code: string;
  subject_name: string;
  years: string[];
  a_total: number[];
  a_single_100: number[];
  a_single_over100: number[];
  b_total: number[];
  b_single_100: number[];
  b_single_over100: number[];
}
// 国家线数据
const nationalLineData: Ref<NationalLineData> = ref({
  subject_code: "",
  subject_name: "",
  years: [],
  a_total: [],
  a_single_100: [],
  a_single_over100: [],
  b_total: [],
  b_single_100: [],
  b_single_over100: [],
});

// 学习计划数据状态
const studyPlanData = ref<StudyPlanData>({
  weakModuleAnalysis: [],
  studyPlanning: {
    title: "初试各科学习规划",
    stages: [],
  },
  comprehensiveAdvice: "",
});

// 薄弱模块分析数据（用于显示）
const weakModuleData = ref<WeakModuleAnalysis[]>([]);

// 图表相关
const echartsARef = ref<HTMLDivElement>();
const echartsBRef = ref<HTMLDivElement>();
let chartA: ECharts | null = null;
let chartB: ECharts | null = null;
const idCode = ref<string>('');
// 根据专业代码获取一级学科
function getFirstLevelSubject(code: string) {
  if (!code || code.length < 2) return "";

  // 根据专业代码前两位确定一级学科
  const subjectCode = code.substring(0, 2);
  const subjectMap : Record<string, string> = {
    "01": "哲学",
    "02": "经济学",
    "03": "法学",
    "04": "教育学",
    "05": "文学",
    "06": "历史学",
    "07": "理学",
    "08": "工学",
    "09": "农学",
    "10": "医学",
    "11": "军事学",
    "12": "管理学",
    "13": "艺术学",
  } ;

  return subjectMap[subjectCode] || "";
}
onMounted(() => {
  const paramsIdCode = route.currentRoute.value.params.idcode;
  idCode.value = Array.isArray(paramsIdCode) ? paramsIdCode[0] : paramsIdCode || '';

  reportByCode(idCode.value).then((res) => {
   
    studentInfo.value = res.data.studentInfo;
    reportInfo.value = res.data.report_info;
    if (studentInfo.value && studentInfo.value.majorCode) {

      studentInfo.value.firstLevelSubject = getFirstLevelSubject(studentInfo.value.majorCode);
    }
    reportData.value = res.data.report;
    getnationline();
    is_show.value = false;

  });
  fetchAndRenderStudyPlan();
  fetchTargetScore();
  const getnationline =  (): void =>  {
    if (!reportInfo.value?.major_code) return;
    let majorArr =  reportInfo.value?.major_code.split(',');
    let tmpCode = majorArr[0].substring(0,4);
     getNationalLineData(
             tmpCode
      ).then((res) => {
        console.log(res);
        nationalLineData.value = res.data;
          initCharts();
         //window.addEventListener("resize", handleResize);
      });
  };
});

// 从数据库获取数据并渲染
const fetchAndRenderStudyPlan = async () => {
  try {
    // 调用API从数据库获取学习计划数据
    const response = await getStudyPlanFromDatabaseEdit({ report_id: idCode.value });
    if (response.code === 0 && response.data) {
      studyPlanData.value = response.data;

      console.log("开始渲染学习计划数据:",  studyPlanData.value);
    } else {
      console.error("获取学习计划失败:", response.msg);
      ElMessage.error(response.msg || "获取学习计划失败");
    }
  } catch (error) {
    console.error("获取学习计划异常:", error);
    ElMessage.error("获取学习计划失败");
  }
};
interface ScoreData {
  politics: number,
  english: number,
  business1: number,
  business2: number,
  total: number

}
const targetScore = ref<ScoreData>();
// 从数据库获取数据并渲染
const fetchTargetScore = async () => {
  try {
    // 调用API从数据库获取学习计划数据
    const response = await getTargetScoreDetail( idCode.value );
    if (response.code === 0 && response.data) {
      targetScore.value = response.data;

      console.log("开始渲染学习计划数据:",  targetScore.value);
    } else {
      console.error("获取学习计划失败:", response.msg);
      ElMessage.error(response.msg || "获取学习计划失败");
    }
  } catch (error) {
    console.error("获取学习计划异常:", error);
    ElMessage.error("获取学习计划失败");
  }
};

// 处理窗口大小变化
const handleResize = (): void => {
  // 如果组件不可见，不需要调整图表大小
 // if (!isVisible.value) return;

  // 使用 setTimeout 确保在 DOM 更新后调整大小
 // setTimeout(() => {
    if (chartA) {
      chartA.resize();
    }
    if (chartB) {
      chartB.resize();
    }

    // 如果图表未初始化或者大小不正确，重新初始化
   // if ((!chartA || !chartB) && isVisible.value) {
      initCharts();
    //}
  //}, 0);
};
const chartAtext = ref('');
const chartBtext = ref('');
const chartOption = (
  isARegion: boolean = true
): EChartsOption => {
  // 根据区域选择对应的数据
  const years: string[] =
    nationalLineData.value.years.length > 0
      ? nationalLineData.value.years
      : ["2021", "2022", "2023", "2024", "2025"];

  const totalScores: number[] = isARegion
    ? nationalLineData.value.a_total.length > 0
      ? nationalLineData.value.a_total
      : [360, 370, 360, 370, 370]
    : nationalLineData.value.b_total.length > 0
    ? nationalLineData.value.b_total
    : [340, 350, 340, 350, 350];

  const single100Scores: number[] = isARegion
    ? nationalLineData.value.a_single_100.length > 0
      ? nationalLineData.value.a_single_100
      : [60, 65, 60, 65, 65]
    : nationalLineData.value.b_single_100.length > 0
    ? nationalLineData.value.b_single_100
    : [55, 60, 55, 60, 60];

  const singleOver100Scores: number[] = isARegion
    ? nationalLineData.value.a_single_over100.length > 0
      ? nationalLineData.value.a_single_over100
      : [90, 95, 90, 95, 95]
    : nationalLineData.value.b_single_over100.length > 0
    ? nationalLineData.value.b_single_over100
    : [85, 90, 85, 90, 90];

  console.log(`${isARegion ? "A区" : "B区"}图表数据:`, {
    years,
    totalScores,
    single100Scores,
    singleOver100Scores,
  });


  return {
    title: {
      text: '',
      left: "left",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#1bb394",
      },
      top: 10,
    },
    grid: {
      left: 10,
      right: 20,
      top: 30,
      bottom: 15,
      containLabel: true,
    },
    legend: {
      data: ["总分", "单科(满分=100)", "单科(满分>100)"],
      right: 10,
      top: 10,
      icon: "rect",
      itemWidth: 16,
      itemHeight: 8,
      textStyle: {
        fontSize: 12,
      },
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0,0,0,0.7)",
      borderRadius: 8,
      textStyle: { color: "#fff" },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: years,
      axisLine: { lineStyle: { color: "#1bb394" } },
      axisLabel: { color: "#666" },
    },
    yAxis: {
      type: "value",
      min: 0,
      max: 400,
      interval: 100,
      splitLine: { lineStyle: { color: "#eee" } },
      axisLine: { show: false },
      axisLabel: { color: "#666" ,fontSize: 6},
    },
    series: [
      {
        name: "总分",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: "rgba(255, 153, 0, 0.15)",
          origin: "start",
        },
        lineStyle: { color: "#ff9900", width: 2 },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#ff9900",
          fontWeight: "bold",
          offset: [0,3],
          formatter: function (params: any) {
            return params.value;
          },
        },
        data: totalScores,
      },
      {
        name: "单科(满分=100)",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: "rgba(27, 179, 148, 0.15)",
          origin: "start",
        },
        lineStyle: { color: "#1bb394", width: 2 },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#1bb394",
          fontWeight: "bold",
          offset: [0, 5],
          formatter: function (params: any) {
            return params.value;
          },
        },
        data: single100Scores,
      },
      {
        name: "单科(满分>100)",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: "rgba(255, 99, 132, 0.10)",
          origin: "start",
        },
        lineStyle: { color: "#ff6384", width: 2 },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#ff6384",
          fontWeight: "bold",
          offset: [0, -5],
          formatter: function (params: any) {
            return params.value;
          },
        },
        data: singleOver100Scores,
      },
    ],
  };
};
const initCharts = (): void => {
  //console.log("initCharts 被调用，isVisible:", isVisible.value);

  // 检查组件是否可见，如果不可见则不初始化图表
  // if (!isVisible.value) {
  //   console.log("组件不可见，跳过图表初始化");
  //   return;
  // }

  // 使用 setTimeout 确保 DOM 已完全渲染并且有正确的尺寸
  // setTimeout(() => {
    console.log("开始初始化图表...");
    console.log("echartsARef.value:", echartsARef.value);
    console.log("echartsBRef.value:", echartsBRef.value);


    if (echartsARef.value) {
      // 检查容器是否有尺寸
      const rect = echartsARef.value.getBoundingClientRect();
      console.log("A区图表容器尺寸:", rect);
      console.log("A区容器样式:", window.getComputedStyle(echartsARef.value));

      if (rect.width > 0 && rect.height > 0) {
        try {
          // 如果已经初始化过，先销毁
          if (chartA) {
            console.log("销毁已存在的A区图表");
            chartA.dispose();
          }
          console.log("开始初始化A区图表");
          chartA = echarts.init(echartsARef.value);
          const optionA = chartOption(true);
          console.log("A区图表配置:", optionA);
          chartA.setOption(optionA); // A区图表
          console.log("A区图表初始化成功");
          if (
                  studentInfo.value &&
                  studentInfo.value.targetMajorName &&
                  studentInfo.value.majorCode
          ) {
            let majorName = studentInfo.value.targetMajorName;
            let majorCode = studentInfo.value.majorCode;
            let firstLevelSubject = studentInfo.value.firstLevelSubject || "";
            chartAtext.value   =  `A区  专业名称：${majorName}  专业代码：${majorCode}  一级学科：${firstLevelSubject}`;
          }


        } catch (error) {
          console.error("A区图表初始化失败:", error);
        }
      } else {
        console.warn("A区图表容器尺寸为0，延迟初始化");
        // 如果容器尺寸为0，延迟重试
       // setTimeout(() => initCharts(), 200);
        return;
      }
    } else {
      console.warn("A区图表容器不存在");
    }

    if (echartsBRef.value) {
      // 检查容器是否有尺寸
      const rect = echartsBRef.value.getBoundingClientRect();
      console.log("B区图表容器尺寸:", rect);
      console.log("B区容器样式:", window.getComputedStyle(echartsBRef.value));

      if (rect.width > 0 && rect.height > 0) {
        try {
          // 如果已经初始化过，先销毁
          if (chartB) {
            console.log("销毁已存在的B区图表");
            chartB.dispose();
          }
          console.log("开始初始化B区图表");
          chartB = echarts.init(echartsBRef.value);
          const optionB = chartOption(false);
          console.log("B区图表配置:", optionB);
          chartB.setOption(optionB); // B区图表
          if (
                  studentInfo.value &&
                  studentInfo.value.targetMajorName &&
                  studentInfo.value.majorCode
          ) {
            let majorName = studentInfo.value.targetMajorName;
            let majorCode = studentInfo.value.majorCode;
            let firstLevelSubject = studentInfo.value.firstLevelSubject || "";
            chartBtext.value   =  `B区  专业名称：${majorName}  专业代码：${majorCode}  一级学科：${firstLevelSubject}`;
          }
          console.log("B区图表初始化成功");
        } catch (error) {
          console.error("B区图表初始化失败:", error);
        }
      } else {
        console.warn("B区图表容器尺寸为0，延迟初始化");
        // 如果容器尺寸为0，延迟重试
       // setTimeout(() => initCharts(), 200);
        return;
      }
    } else {
      console.warn("B区图表容器不存在");
    }
  // }, 200); // 增加延迟时间，确保DOM完全渲染
};

// 计算总分
const calculateTotalScore = () => {

  const politics = parseFloat(studentInfo.value?.politics ?? '0') || 0;
  const english = parseFloat(studentInfo.value?.englishS ??'0') || 0;
  const math = parseFloat(studentInfo.value?.mathScore?.toString() ?? '0') || 0;
  const professional = parseFloat(studentInfo.value?.professionalScore?.toString()??'0') || 0;
// console.log(calculateTotalScore)
  return (politics + english + math + professional).toString();

 
};
</script>

<style scoped>
.home-bg {
  height: 100vh;
  width: 100vw;  
  overflow-y: auto;
  /* 手机端适配 */
  display: block;
  flex-direction: column;
  align-items: center;
  font-family: PingFang SC, PingFang SC;
}
.content-wrapper {
  display: block;
  width: 100%;
  background: url('@/assets/mobile/page.png') no-repeat top center;
  background-size: cover;
  max-width: 750px; /* 适配主流手机宽度 */
  /* margin: 0 auto; */
  height: 85vh;
  padding: 32px 11px 592px 168px;
  box-sizing: border-box;
}
.logo-wrapper { 
  display: flex;
}
.logo {
  display: flex;
  width: 160px;
  margin: 30px auto 32px 75%;
  text-align: right;
}
.title {
  text-align: right;
  font-size: 60px;
  margin-top:9vh;
  margin-right:32px;
  font-weight: bold;
  color: #2bbfae;
  margin-bottom: 48px;
}
.section {
  margin-bottom: 40px;
  font-size: 24px;
  text-align: right;
  margin-right: 136px;
  margin-top:126px;
}
.card {
  width: 100%; /* 卡片宽度 */
  max-width: 750px; /* 最大宽度，略小于content-wrapper */
  padding: 0 30px;
  background-color: #fff; /* 卡片背景色 */
  box-shadow: 0 8px 24px rgba(0,0,0,0.1);
  box-sizing: border-box;
  
}
.info-section-title-bg {
  padding:10px 0;
  width: 100%;
}
.title-bg-img {
  width: 100%;
  height: auto;
  display: block;
}
.step-num-tag {
  background-image: url("@/assets/mobile/first_title.png");
  width: 400px;
  height: 54px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
}
.school-step-num-tag {

}
.step-num-tag span {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size:16px;
  border-radius: 50%;
  margin-right: 38px;
  font-weight: bold;
  padding-left: 26px;
}

.tag-text {
  color: #1bb394;
  font-weight: bold;
  font-size:16px;
}
/* 表格样式 */
.score-table {
  width: 680px;
  border: 1px solid #1bb394;
  margin-top: 10px;
  margin-bottom: 10px;
  border-radius: 8px;
  overflow: hidden;
  margin-left: 5px;
}
.target_score-table{
  margin:19px 0 20px 0;
}

.table-header {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  text-align: center;
  font-weight: bold;
  border-bottom: 1px solid #1bb394;
  background: #F5FFFD;
}

.th-cell {
  padding: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 12px;
  border-right: 1px solid #1bb394;
  text-align: center;
  height: 20px;
}
.th-cell:last-child {
    border-right: none;
  }

.th-cell .sel-no-border {
    :deep(.el-select__wrapper) {
      border-color: #fff !important;
      box-shadow: none;
    }

  .th-cell :deep(.el-select__selected-item) {
      text-align: center;
    }


  .center-select {
    width: 100%;

    :deep(.el-input__inner) {
      text-align: center !important;
    }

    :deep(.el-input__suffix) {
      right: 5px;
    }
  }

  :deep(.center-select-dropdown) {
    .el-select-dropdown__item {
      text-align: center !important;
    }
  }
}

.table-row-score {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  text-align: center;
}

.td-cell {
  font-size: 16px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #1bb394;
  text-align: center;
  height: 20px;
}
 .td-cell:last-child {
    border-right: none;
  }

.td-cell :deep(.el-input__wrapper) {
    box-shadow: none;
  }

.td-cell :deep(.el-input__inner) {
    text-align: center;
    box-shadow: none;
    font-weight: bold;
  }


.td-cell .el-input {
  width: 100%;
}
.personal-demands,
.expertise-advice {
  margin-left: 5px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.demands-label,
.advice-label {
  font-size: 16px;
  color: #333;
  width: 680px;
  background: #FFFFFF;
  padding:8px 16px;
  border-radius: 4px;
  border: 1px solid #1BB394
}
.demands-label-title,
.advice-label-title{
  font-weight: bold;
}
.demands-input,
.advice-input {
  flex: 1;
}
.info-block-content {
  margin:10px 0 0 0;
  display: flex;
  font-size: 16px;
  justify-content: flex-start;
  align-items: flex-start;
  text-align: left;
  flex-wrap: wrap;
  padding-left:10px;
}
.info-block-item{
    display: flex;
    max-width:33.33%;
    flex: 1 0 33.33%;
    margin-bottom: 8px; /* 添加底部间距，使行间有一定空隙 */
    box-sizing: border-box; /* 确保padding和border计入宽度 */
    padding-right: 10px; /* 添加右侧padding，防 */
    align-items: center;
}
.major-block {
  max-width:100%;
  flex: 1 0 100%;
  line-height: 1.5;

}
.major-block .info-block-item-title {
  min-width: 80px;
}
/* 添加以下样式定义 */
.info-block-item-title,
.info-block-item-content {
    display: inline-block;
    vertical-align: middle; /* 确保文本垂直居中 */
    line-height: 1.1; /* 设置合适的行高 */
    font-size: 16px; /* 保持与现有字体大小一致 */
}


.info-block-item-title {
  padding-right:2px;
  font-weight: bold;
  color: #5A5A5A;
  text-transform: none;
}
.first_stage{
  font-size: 20px;
  font-weight: bold;
  margin-top: 13px;
  margin-left:4px;
}

.stage_module-title {
  font-size: 16px;
  font-weight: bold;
}
.first_stage_content {
  white-space: pre-line;
  margin-top:20px;
  padding:26px 28px;
  background: linear-gradient( 180deg, #f2fcf7 0%, rgba(255,255,255,0) 100%);
  border-radius: 14px 14px 14px 14px;
}
.stage_module{ 
  font-size: 20px;
  font-weight: bold;
}
.report_suggest {
  padding: 25px 22px;
}
.report_suggest_title{
  font-size: 20px;
  font-weight: bold;
}
.report_suggest_content{
  white-space: pre-line;
  word-break: break-word;  
  margin-top: 16px;
  font-size: 16px;
  line-height: 24px;
}
.stage_module{
  height:34px;
  line-height: 34px;
}
.info-other-block-content { 
  margin:4px 0;
  display: flex;
  font-size: 16px;
  justify-content: flex-start;
  align-items: flex-start;
  text-align: left;
  flex-wrap: wrap;
  padding-left:10px;
}
.info-other-block-item{
    display: flex;
    flex: 1 0 50%;
    margin-bottom: 4px; /* 添加底部间距，使行间有一定空隙 */
    box-sizing: border-box; /* 确保padding和border计入宽度 */
    padding-right: 1px; /* 添加右侧padding，防 */
    align-items: center;
}
.info_area {
  flex:2;
}
/* 添加以下样式定义 */
.info-other-block-item-title,
.info-other-block-item-content {
    display: inline-block;
    vertical-align: middle; /* 确保文本垂直居中 */
    line-height: 1.1; /* 设置合适的行高 */
    font-size: 16px; /* 保持与现有字体大小一致 */
}

.info-other-block-item-title {
  margin-right:2px;
  font-weight: bold;
  color: #5A5A5A;
  text-transform: none;
}

.step-content {
  width: 100%;
  margin-left:8px;
}
.subject_info{
  font-size:16px;
  margin-left: 8px;
  margin-bottom: 6px;
}
/* 图表样式 */
.echarts-box {
  width: 98%;
  min-height: 256px;
  margin: 0 auto 18px auto;
  border: 1px solid #1bb394;
  border-radius: 12px;
  background: #fff;
  box-sizing: border-box;
}
/* 学校表格样式 */
.school-table-container {
  width: 100%;
  font-size: 14px;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 20px;
}

.school-table-header {
  display: grid;
  grid-template-columns: 0.8fr 2fr 1fr 1fr 1fr 1fr 1fr 0.8fr 1.2fr;
  background-color: #dcf7f0;
  color: #333;
  height: 46px;
  line-height: 46px;
  text-align: center;
  /* padding: 0 5px; */
}

.header-cell {
  padding: 0 2px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.school-table-body {
  background-color: #fff;
}

.table-row {
  display: grid;
  grid-template-columns: 0.8fr 2fr 1fr 1fr 1fr 1fr 1fr 0.8fr 1.2fr;
  font-size:14px;
  height: 60px;
  line-height: normal;
  text-align: center;
  padding: 0 1px;
  align-items: center;
}
.table-row:hover {
    background-color: #f5f7fa;
  }

.table-row:nth-child(even) {
  background-color: #F5FFFD;
}
.table-row:nth-child(even):hover {
      background-color: #f5f7fa;
    }



.body-cell {
  padding: 0 1px;
  font-size: 14px;
  color: #333;
  word-break: break-all;
  white-space: normal;
  overflow: visible;
  display: block;
  -webkit-line-clamp: unset;
  -webkit-box-orient: unset;
  max-width: 100%;
  box-sizing: border-box;
}

.school-name {
  text-align: center;
  font-weight: bold;
  position: relative;
  line-height:60px;
}

.school-tags {
  position: absolute;
  top: 50px;
  left: 50%;
  display: flex;
  gap: 4px;
  transform: translate(-50%, -50%);
  font-weight: normal;
  justify-content: center;
  align-items: center;
  min-width:100px;
}

.tag {
  display: inline-block;
  padding: 1px;
  line-height: 1.5;
  font-size: 6px !important;
  color: white;

}
.tag_other {
  padding: 2px 4px;
}

.tag-985 {
  background-color: #CAA0FF;
}

.tag-211 {
  background-color: #92BAFF;
}

.tag-double {
  background-color: #FF9B3A;
}
/* 院校详情卡片样式 */
.school-detail-card {
  background-color: #fff;
  margin-top:17px;
  overflow: hidden;
}

.school-header {
  display: flex;
}

.school-logo {
  width: 78px;
  overflow: hidden;
  margin-right: 7px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.school-logo  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: contain;

}

.school-info {
  flex: 1;
}

.school-title {
  display: flex;
  align-items: baseline;
  margin-bottom: 16px;
}
.school-title h2 {
    font-size: 22px;
    color: #333;
    margin: 0;
    min-width: 180px;
    margin-right: 12px;
  }

.school-title .school-location {
    font-size: 16px;
    color: #666;
  }


.school-tags-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}
 .school-tags-row  .tag {
    margin-right: 0;
  }

.school-tags-row .major-diff {
    font-size: 14px;
    color: #333;
    margin-left: 5px;
  }


.section-title {
  font-size: 20px;
  margin: 15px 0;
  position: relative;
  padding-left: 15px;
  font-weight:bold;
  color:#4C5370;
}
.section-title-font{
  font-family: Microsoft YaHei, Microsoft YaHei;
  color: #4C5370;

}

.school-detail-section {
  margin-bottom: 5px;
  padding: 0 14px 5px;
  border-radius: 12px 12px 12px 12px;
  border: 1px dashed #1BB394;
  background: linear-gradient( 180deg, #f2fcf7 0%, rgba(255,255,255,0) 60%);
}

  .detail-item {
    display: flex;
    margin-bottom: 5px;

  }
  .detail-item :last-child {
    margin-bottom: 0;
  }


  .item-icon {
    width: 19px;
    height: 15px;
    margin-right: 10px;
    flex-shrink: 0;
  }
  .item-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }


  .item-content {
    flex: 1;
  }

  .item-content  h4 {
    font-size: 16px;
    color: #4C5370;;
    padding: 0 0 8px 0;
    font-weight: 600;
  }

  .item-content p {
    font-size: 16px;
    color: #504E4E;
    line-height: 1.6;
    margin: 0 0 5px 0;
  }
 .item-content p:last-child {
      margin-bottom: 0;
    }



/* 招生情况样式 */
.step-content-zs {
  width: 100%;
  margin-top: 10px;
}

.admission-title {
  font-size: 20px;
  font-weight: bold;
  color: #4C5370;
  margin: 10px 0 ;
  position: relative;
  padding-left: 12px;
}

.admission-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 10px;
  border-radius: 8px;
  overflow: hidden;
}

.admission-table th {
  background-color: #dcf7f0;
  color: #333;
  font-size: 16px;
  font-weight: bold;
  padding: 12px 1px;
  text-align: center;
  border: 1px solid #e8f5f0;
}

.admission-table td {
  padding: 12px 1px;
  font-size: 14px;
  text-align: center;
  color: #5A5A5A;
  background-color: #fff;
}

.admission-table tr:nth-child(even) td {
  background-color: #F5FFFD;
}

.reexam-container {
  border: 1px dashed #1bb394;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 10px;
  background: linear-gradient( 180deg, #f2fcf7 0%, rgba(255,255,255,0) 60%);

}

.reexam-card {
  padding: 0 14px 5px;
}

.reexam-card:last-child {
  border-bottom: none;
}

.reexam-header {
  display: flex;
  align-items: center;
  padding: 8px 0;
}
.reexam-header img {
    width: 19px;
    height: 15px;
    margin-right: 8px;
  }


.reexam-title {
  font-size: 16px;
  font-weight: bold;
  color: #4C5370;
}

.reexam-content {
  color: #504E4E ;
  line-height: 1.6;
  font-size: 16px;
  margin-left:16px;
}

.reexam-content p {
  margin: 8px 0;
}
.last-reexam-content {
  padding-bottom: 6px;
}

/* 推荐综合性价比高的院校样式 */
.recommend-school-container {
  margin-top: 20px;
  margin-left:4px;
  border: 1px dashed #1bb394;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
  background-color: #fff;
  background: linear-gradient( 180deg, #f2fcf7 0%, rgba(255,255,255,0) 60%);
}

.recommend-school-card {
  padding: 0 20px 20px;
}

.recommend-school-header {
  display: flex;
  align-items: center;
  padding: 15px 0 0 0;
}

.recommend-icon {
  margin-right: 8px;
}
.recommend-icon   img {
    width: 54px;
    height: 42px;
  }


.recommend-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.recommend-school-content {
  color: #666;
  line-height: 1.6;
  font-size: 16px;
  margin-left:16px;
}

.recommend-school-content p {
  margin: 7px 0;
}

/* 您已有的 @media 查询保持不变 */
@media (max-width: 750px) {
  .content-wrapper {
    max-width: 100vw;
    padding: 12px 2vw 50px 2vw;
  }
  .card {
    width: 100%;
    margin-top: 0px; /* 移除了原来的负边距值 */
    padding-bottom:30px;
  }
}

/* 添加新的样式 */
.info-section .el-table {
  margin-top: 20px;
}

.info-block p {
  margin: 10px 0;
  font-size: 14px;
  color: #555;
}
</style>