<template>
  <div
    class="page-footer"
    style="position: relative; width: 100%; height: 80px"
  >
    <div class="footer-bg" style="position: relative; height: 100%">
      <div
        class="page-number"
        style="
          background-image: url('https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/bottom-left-bg.png');
          background-repeat: no-repeat;
          background-size: contain;
          width: 70px;
          height: 20px;
          position: absolute;
          bottom: 20px;
          right: 40px;
          font-weight: bold;
          font-size: 14px;
          padding-left: 18px;
          line-height: 20px;
          color: #333;
        "
      >
        {{ pageNumber }} / {{ totalPages }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";
const props = defineProps({
  pageNumber: {
    type: Number,
    default: 1,
  },
  totalPages: {
    type: Number,
    default: 1,
  },
});
</script>

<style lang="scss" scoped></style>
