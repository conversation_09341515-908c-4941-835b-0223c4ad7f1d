<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use support\Request;

return [
    'debug' => true,
    'error_reporting' => E_ALL,
    'default_timezone' => 'Asia/Shanghai',
    'request_class' => Request::class,
    'public_path' => base_path() . DIRECTORY_SEPARATOR . 'public',
    'runtime_path' => base_path(false) . DIRECTORY_SEPARATOR . 'runtime',
    'controller_suffix' => 'Controller',
    'controller_reuse' => false,
    'jwt_key' => 'your-secret-key-here',

    'check_spider_login_url'=> getenv("SPIDER_LOGIN_URL") ?: "http://spider.yanqukaoyan.com/api/check_login_status",
    'start_spider_school_info_url'=>getenv("SPIDER_SCHOOL_INFO_URL") ?: "http://spider.yanqukaoyan.com/api/crawl_school_info",
    'start_spider_school_info_url_sync'=>getenv("SPIDER_SCHOOL_INFO_URL_SYNC") ?: "http://spider.yanqukaoyan.com/api/crawl_school_info_sync",
    'start_spider_school_info_url_simple'=>getenv("SPIDER_SCHOOL_INFO_URL_SIMPLE") ?: "http://spider.yanqukaoyan.com/api//crawl_school_info_sync_limited"
];
