<?php
use <PERSON>man\Route;

// 报告相关接口 - 需要鉴权
Route::group("/api", function () {
    // 报告列表和详情
    Route::get('/report', [app\controller\ReportController::class, 'reportList']);
    Route::get('/report/detail', [app\controller\ReportController::class, 'getReportDetail']);
    Route::get('/report/basic-info', [app\controller\ReportController::class, 'getReportBasicInfo']);
    Route::get('/report_info', [app\controller\ReportController::class, 'getReportInfo']);
    Route::get('/report_count', [app\controller\ReportController::class, 'getReportCount']);
    
    // 报告保存和更新
    Route::post('/save_report', [app\controller\ReportController::class, 'saveReport']);
    Route::put('/report/pdf-url', [app\controller\ReportController::class, 'updatePdfUrl']);
    
    // 系统配置和工具
    Route::get('/get_years', [app\controller\ReportController::class, 'getExamYears']);
    Route::get('/check_spider_login', [app\controller\ReportController::class, 'checkSpiderLogin']);
    Route::get('/coskey', [app\controller\ConfigController::class, 'getCosKey']);
    Route::post('/uploadfile', [app\controller\ConfigController::class, 'uploadFile']);

    //生成idcode
    Route::post('/generate_code', [app\controller\ReportController::class, 'generateIdCode']);

    //删除院校
    Route::post('/report/delete_school', [app\controller\ReportController::class, 'deleteSchool']);
})->middleware([app\middleware\JwtMiddleware::class]);
