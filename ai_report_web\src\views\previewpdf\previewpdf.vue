<template>
  <div class="generate-pdf-container">
    <!-- Loading 遮罩层 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载报告数据...</div>
        <div class="loading-progress">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: loadingProgress + '%' }"
            ></div>
          </div>
          <div class="progress-text">{{ loadingProgress }}%</div>
        </div>
      </div>
    </div>

    <div class="report-container" v-show="!isLoading">
      <!-- 报告封面头部区域 -->
      <div class="report-header">
        <div class="logo">
          <img
            src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/pdf_logo.png"
          />
        </div>
        <div class="stu-name">姓名：{{ reportForm.name }}</div>
      </div>
      <div class="report-content-container">
        <!-- 报告内容页面 -->
        <div class="report-content">
          <div class="first-step">
            <div class="tite-img-container">
              <img
                src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/first-step-title.png"
                alt="第一步"
                class="step-title-bg"
              />
            </div>

            <!-- 第一步：个人基础信息 -->
            <div class="step-section">
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">1</span>
                  <span class="step-text">个人基础信息</span>
                </div>
              </div>
              <div class="step-content">
                <div class="form-grid">
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">学员姓名：</span>
                      <span class="value">{{ reportForm.name }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">性别：</span>
                      <span class="value">{{ reportForm.sex }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">本科院校：</span>
                      <span class="value">{{
                        reportForm.undergraduateSchool
                      }}</span>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">本科专业：</span>
                      <span class="value">{{
                        reportForm.undergraduateMajor
                      }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">目标专业：</span>
                      <span class="value">{{
                        truncateText(reportForm.targetMajor, 6)
                      }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">专业代码：</span>
                      <span class="value">{{
                        truncateText(reportForm.majorCode, 6)
                      }}</span>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">联系方式：</span>
                      <span class="value">{{ reportForm.phone }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">考研年份：</span>
                      <span class="value">{{ reportForm.examYear }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">跨专业：</span>
                      <span class="value">{{
                        reportForm.isMultiDisciplinary
                      }}</span>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-item full-width">
                      <span class="label">培养方式：</span>
                      <span class="value">{{
                        reportForm.educationalStyle
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 第二步：本科成绩情况 -->
            <div class="step-section">
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">2</span>
                  <span class="step-text">本科成绩情况</span>
                </div>
              </div>
              <div class="step-content">
                <div class="form-undergraduate">
                  <div
                    class="form-item"
                    v-for="item in reportForm.undergraduateTranscript"
                    :key="item.id"
                  >
                    <span class="label">{{ item?.title }}：</span>
                    <span class="value">{{ item?.score || "" }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 第三步：英语基础 -->
            <div class="step-section">
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">3</span>
                  <span class="step-text">英语基础</span>
                </div>
              </div>
              <div class="step-content">
                <div class="form-grid">
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">高考英语成绩：</span>
                      <span class="value">{{
                        reportForm.englishScore || ""
                      }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">大学四级：</span>
                      <span class="value">{{ reportForm.cet4 || "" }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">大学六级：</span>
                      <span class="value">{{ reportForm.cet6 || "" }}</span>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">托福：</span>
                      <span class="value">{{
                        reportForm.tofelScore || ""
                      }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">雅思：</span>
                      <span class="value">{{
                        reportForm.ieltsScore || ""
                      }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">英语能力：</span>
                      <span class="value">{{
                        reportForm.englishLevel || ""
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 第四步：目标院校倾向 -->
            <div class="step-section">
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">4</span>
                  <span class="step-text">目标院校倾向</span>
                </div>
              </div>
              <div class="step-content">
                <div class="form-grid">
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">地区倾向：</span>
                      <span class="value">{{ reportForm.region }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">省份倾向：</span>
                      <span class="value">{{
                        reportForm.intendedSchools
                      }}</span>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">梦校：</span>
                      <span class="value">{{
                        reportForm.targetSchoolName
                      }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">院校层次：</span>
                      <span class="value">{{ reportForm.schoolLevel }}</span>
                    </div>
                  </div>
                  <div class="form-row full-width">
                    <div class="form-item">
                      <span class="label">专业课指定参考书：</span>
                      <span class="value">{{ reportForm.referenceBooks }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 第五步：考研成绩预估 -->
            <div class="step-section">
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">5</span>
                  <span class="step-text">考研成绩预估</span>
                </div>
              </div>
              <div class="step-content">
                <div class="score-table">
                  <table>
                    <thead>
                      <tr>
                        <th>政治</th>
                        <th>英语</th>
                        <th>业务课一</th>
                        <th>业务课二</th>
                        <th>总分</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>{{ reportForm.politics || "" }}</td>
                        <td>{{ reportForm.englishS || "" }}</td>
                        <td>{{ reportForm.englishType || "" }}</td>
                        <td>{{ reportForm.mathType || "" }}</td>
                        <td>{{ reportForm.totalScore || "" }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="weak-modules">
                  <span class="label">个性化需求：</span>
                  <span class="value">{{
                    reportForm.personalNeeds || ""
                  }}</span>
                </div>

                <div class="weak-modules">
                  <span class="label">薄弱环节：</span>
                  <span class="value">{{ reportForm.weakModules || "" }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二页专业分析 -->
        <!-- 报告内容页面 -->
        <div class="report-content">
          <div class="first-step">
            <div class="tite-img-container">
              <img
                src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/step_two_major.png"
                alt="第二步"
                class="step-title-bg"
              />
            </div>

            <!-- A区图表 -->
            <div class="chart-section">
              <div class="chart-header">
                <span class="region-label">A区</span>
                <div class="chart-info">
                  <span
                    >一级学科：{{
                      nationalLineData?.first_level_discipline
                    }}</span
                  >
                  <span>学门门类：{{ nationalLineData?.subject_name }}</span>
                </div>
              </div>
              <div class="chart-container">
                <div ref="chartA" class="echarts-box"></div>
              </div>
            </div>

            <!-- B区图表 -->
            <div class="chart-section">
              <div class="chart-header">
                <span class="region-label">B区</span>
                <div class="chart-info">
                  <span
                    >一级学科{{
                      nationalLineData?.first_level_discipline
                    }}</span
                  >
                  <span>学门门类：{{ nationalLineData?.subject_name }}</span>
                </div>
              </div>
              <div class="chart-container">
                <div ref="chartB" class="echarts-box"></div>
              </div>
            </div>

            <!-- 第三部分 -->
            <div class="tite-img-container">
              <img
                src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/schools_prev.png"
                alt="第三步"
                class="step-title-bg"
              />
            </div>

            <!-- 院校基础信息表格 -->
            <div class="school-info-section">
              <div class="school-table-container">
                <table class="school-info-table">
                  <thead>
                    <tr>
                      <th>序号</th>
                      <th>院校名称</th>
                      <th>所在地区</th>
                      <th>所在城市</th>
                      <th>学院</th>
                      <th>专业</th>
                      <th>专业代码</th>
                      <th>最低分</th>
                      <th>最低总分差</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(school, index) in schoolListData"
                      :key="school.id"
                    >
                      <td>{{ index + 1 }}</td>
                      <td>
                        <div class="school-name">
                          <span>{{ school.school_name }}</span>
                          <div class="school-tags">
                            <span
                              class="tag tag-double"
                              v-if="school.tag_double"
                              >双一流</span
                            >
                            <span class="tag tag-985" v-if="school.tag_985"
                              >985</span
                            >
                            <span class="tag tag-211" v-if="school.tag_211"
                              >211</span
                            >
                          </div>
                        </div>
                      </td>
                      <td>{{ school.region }}</td>
                      <td>{{ school.city }}</td>
                      <td>{{ school.college }}</td>
                      <td>{{ school.major_name }}</td>
                      <td>{{ school.major_code }}</td>
                      <td>{{ school.min_score }}</td>
                      <td>{{ school.score_diff }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- 第四页及后续：院校详细分析 -->
        <div
          class="report-content"
          v-for="(school, index) in realRecommendData"
          :key="index"
        >
          <div class="first-step">
            <!-- 第三部分 -->
            <div class="tite-img-container">
              <img
                src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/school_analysis.png"
                alt="第四步院校分析"
                class="step-title-bg"
              />
            </div>

            <!-- 院校详情卡片 -->
            <div class="school-detail-card">
              <div class="school-header">
                <!-- <div class="school-logo" v-show="school.school_info?.logo">
                <img :src="'https://' + school.school_info?.logo || '@/assets/images/school.png'" alt="学校logo" />
              </div> -->
                <div class="school-logo">
                  <img src="@/assets/images/school.png" alt="学校logo" />
                </div>
                <div class="school-info">
                  <div class="school-title">
                    <h2>{{ school.school_name || "" }}</h2>
                    <span class="school-location">{{
                      school.school_info?.address || ""
                    }}</span>
                  </div>
                  <div class="school-tags-row">
                    <span
                      class="tag tag-double"
                      v-if="school.school_info?.is_dual_class"
                      >双一流</span
                    >
                    <span class="tag tag-985" v-if="school.school_info?.tag_985"
                      >985</span
                    >
                    <span class="tag tag-211" v-if="school.school_info?.tag_211"
                      >211</span
                    >
                    <span class="major-name">{{ school.major_name }}</span>
                    <span class="score-diff"
                      >分差:{{ getDiffScore(parseInt(school.school_id)) }}</span
                    >
                  </div>
                </div>
              </div>

              <template v-if="Object.keys(school.basic_info!).length != 0">
                <h3 class="section-title">院校情况</h3>
                <div class="school-detail-section">
                  <div class="detail-item">
                    <div class="item-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="item-content">
                      <h4>总成绩计算公式</h4>
                      <p>{{ school.basic_info?.score_formula }}</p>
                    </div>
                  </div>

                  <div class="detail-item">
                    <div class="item-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="item-content">
                      <h4>学制</h4>
                      <p>{{ school.basic_info?.study_years }}</p>
                    </div>
                  </div>

                  <div class="detail-item">
                    <div class="item-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="item-content">
                      <h4>学费</h4>
                      <p>学费：{{ school.basic_info?.tuition_fee }}</p>
                    </div>
                  </div>
                </div>

                <h3 class="section-title">初试模块</h3>
                <div class="school-detail-section">
                  <div class="detail-item">
                    <div class="item-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="item-content">
                      <h4>初试考试科目</h4>
                      {{ school.basic_info?.exam_range }}
                    </div>
                  </div>

                  <div class="detail-item">
                    <div class="item-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="item-content">
                      <h4>考试专业课参考书</h4>
                      <p>{{ school.basic_info?.reference_books }}</p>
                    </div>
                  </div>

                  <div class="detail-item">
                    <div class="item-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="item-content">
                      <h4>复试考核内容</h4>
                      <p>{{ school.basic_info?.retest_score_requirement }}</p>
                    </div>
                  </div>
                </div>
              </template>

              <div class="step-content-zs-detail">
                <template
                  v-if="
                    school.admission_data && school.admission_data.length > 0
                  "
                >
                  <div class="admission-title">招生情况</div>
                  <div class="enroll-students-table">
                    <table class="admission-table">
                      <thead>
                        <tr>
                          <th>年份</th>
                          <th>招生计划</th>
                          <th>一志愿复试</th>
                          <th>拟录取</th>
                          <th>录取比</th>
                          <th>调剂人数</th>
                          <th>最高分</th>
                          <th>最低分</th>
                          <th>平均分</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          v-for="(item, idx) in school.admission_data"
                          :key="idx"
                        >
                          <td>{{ item.year }}</td>
                          <td>{{ item.planCount }}</td>
                          <td>{{ item.examCount }}</td>
                          <td>{{ item.admitCount }}</td>
                          <td>{{ item.ratio }}</td>
                          <td>{{ item.studentCount }}</td>
                          <td>{{ item.highestScore }}</td>
                          <td>{{ item.lowestScore }}</td>
                          <td>{{ item.averageScore }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </template>
              </div>

              <!-- 招生情况 -->
              <div class="step-content-zs">
                <template
                  v-if="school.current_year_retest_list.list.length > 0"
                >
                  <!-- 一志愿考试名单标题 -->
                  <div class="admission-title">一志愿复试名单</div>

                  <!-- 一志愿考试名单表格 -->
                  <table class="admission-table">
                    <thead>
                      <tr>
                        <th>编号</th>
                        <th>学生姓名</th>
                        <th>政治</th>
                        <th>英语</th>
                        <th>专业课一</th>
                        <th>专业课二</th>
                        <th>初试成绩</th>
                        <th>是否一志愿</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        v-for="(item, idx) in school.current_year_retest_list
                          .list"
                        :key="idx"
                      >
                        <td>{{ idx + 1 }}</td>
                        <td>{{ item.name + "**" }}</td>
                        <td>{{ item.politics_score }}</td>
                        <td>{{ item.english_score }}</td>
                        <td>{{ item.major1_score }}</td>
                        <td>{{ item.major2_score }}</td>
                        <td>{{ item.initial_score }}</td>
                        <td>{{ item.admission_status }}</td>
                      </tr>
                    </tbody>
                  </table>
                </template>

                <template v-if="school.basic_info?.retest_content">
                  <!-- 复试模块标题 -->
                  <div class="admission-title">复试模块</div>

                  <!-- 复试模块内容 -->
                  <div class="reexam-container">
                    <div class="reexam-card">
                      <div class="reexam-header">
                        <img
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="标签图标"
                        />
                        <div class="reexam-title">复试考试内容</div>
                      </div>
                      <div class="reexam-content">
                        {{ school.basic_info?.retest_content }}
                      </div>
                    </div>

                    <!-- <div class="reexam-card">
                      <div class="reexam-header">
                        <img
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="标签图标"
                        />
                        <div class="reexam-title">录取要求</div>
                      </div>
                      <div class="reexam-content">
                        <p v-if="school.basic_info?.admission_requirements">
                          {{ school.basic_info?.admission_requirements }}
                        </p>
                        <p v-else>暂无数据</p>
                      </div>
                    </div> -->
                  </div>
                </template>

                <template
                  v-if="school.current_year_admission_list?.list.length > 0"
                >
                  <!-- 拟录取名单标题 -->
                  <div class="admission-title">拟录取名单</div>

                  <!-- 拟录取名单表格 -->
                  <table class="admission-table">
                    <thead>
                      <tr>
                        <th>编号</th>
                        <th>学生姓名</th>
                        <th>初试成绩</th>
                        <th>复试成绩</th>
                        <th>两项总成绩</th>
                        <th>一志愿学校</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        v-for="(item, idx) in school.current_year_admission_list
                          .list"
                        :key="idx"
                      >
                        <td>{{ idx + 1 }}</td>
                        <td>{{ item.name + "**" }}</td>
                        <td>{{ item.initial_score }}</td>
                        <td>{{ item.retest_score }}</td>
                        <td>{{ item.total_score }}</td>
                        <td>{{ item.first_choice_school }}</td>
                      </tr>
                    </tbody>
                  </table>
                </template>

                <!-- 综合建议标题 -->
                <div class="admission-title">综合建议</div>

                <!-- 综合建议内容 -->
                <div class="reexam-container">
                  <div class="reexam-card">
                    <div class="reexam-header">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                      <div class="reexam-title">竞争难度分析</div>
                    </div>
                    <div class="reexam-content">
                      {{ school.difficulty_analysis }}
                    </div>
                  </div>

                  <div class="reexam-card">
                    <div class="reexam-header">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                      <div class="reexam-title">备考目标建议</div>
                    </div>
                    <div class="reexam-content">
                      {{ school.suggest }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 页脚 -->
        </div>

        <!-- 推荐综合性价比高的院校页面 -->
        <div class="report-content">
          <!-- 顶部绿色背景区域 -->

          <div class="first-step">
            <div class="recommend-school-wraper">
              <!-- 推荐综合性价比高的院校标题 -->
              <div class="step-num-tag">
                <span class="step-number">{{
                  realRecommendData.length + 1
                }}</span>
                <div class="tag-text">推荐综合性价比高的院校</div>
              </div>

              <!-- 推荐综合性价比高的院校内容 -->
              <div class="recommend-school-container">
                <div class="recommend-school-card">
                  <div class="recommend-school-header">
                    <div class="recommend-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="recommend-title">推荐原因</div>
                  </div>
                  <div class="recommend-school-content">
                    {{ highRecommendData[0]?.reason || "暂无推荐信息" }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 学习计划页面 -->
        <div
          class="report-content"
          v-if="studyPlanData"
          style="padding-bottom: 30px"
        >
          <!-- 顶部绿色背景区域 -->

          <div class="first-step">
            <!-- 学习计划标题图片 -->
            <div class="tite-img-container">
              <img
                src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/study-plan-title-bg.png"
                alt="学习计划"
                class="step-title-bg"
              />
            </div>

            <!-- 薄弱模块分析 -->
            <div
              class="step-section"
              v-if="
                studyPlanData.weakModuleAnalysis &&
                studyPlanData.weakModuleAnalysis.length > 0
              "
            >
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">01</span>
                  <span class="step-text">薄弱模块分析</span>
                </div>
              </div>
              <div class="step-content">
                <div class="weak-module-container">
                  <div
                    v-for="(
                      weakModule, moduleIndex
                    ) in studyPlanData.weakModuleAnalysis"
                    :key="moduleIndex"
                    class="weak-module-item"
                  >
                    <!-- 科目 -->
                    <div class="detail-section">
                      <div class="subtitle">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="标签图标"
                        />
                        <span class="subtitle-text">科目</span>
                      </div>
                      <p class="detail-text">{{ weakModule.subject }}</p>
                    </div>

                    <!-- 问题分析 -->
                    <div
                      class="analysis-section"
                      v-if="weakModule.problemAnalysis"
                    >
                      <div class="subtitle">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="标签图标"
                        />
                        <span class="subtitle-text">问题分析</span>
                      </div>
                      <p class="analysis-text">
                        {{ weakModule.problemAnalysis }}
                      </p>
                    </div>

                    <!-- 解决方案 -->
                    <div class="solutions-section" v-if="weakModule.solutions">
                      <div class="subtitle">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="标签图标"
                        />
                        <span class="subtitle-text">解决方案</span>
                      </div>
                      <div class="solutions-content">
                        <p class="solution-text">{{ weakModule.solutions }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 目标分数表格 -->
            <div class="target-score-section" v-if="targetScoreData">
              <div data-v-e1d12685="" class="step-header">
                <div data-v-e1d12685="" class="step-num-tag">
                  <img
                    data-v-e1d12685=""
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  /><span data-v-e1d12685="" class="step-number">02</span
                  ><span data-v-e1d12685="" class="step-text">各目标分数</span>
                </div>
              </div>

              <div class="target-score-table">
                <div class="score-row header-row">
                  <div class="score-cell">政治</div>
                  <div class="score-cell">英语</div>
                  <div class="score-cell">业务课一</div>
                  <div class="score-cell">业务课二</div>
                  <div class="score-cell">总分</div>
                </div>
                <div class="score-row data-row">
                  <div class="score-cell bottom-cell">
                    {{ targetScoreData.politics || 0 }}
                  </div>
                  <div class="score-cell bottom-cell">
                    {{ targetScoreData.english || 0 }}
                  </div>
                  <div class="score-cell bottom-cell">
                    {{ targetScoreData.business1 || 0 }}
                  </div>
                  <div class="score-cell bottom-cell">
                    {{ targetScoreData.business2 || 0 }}
                  </div>
                  <div class="score-cell bottom-cell">
                    {{ targetScoreData.total || 0 }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 学习规划模块 -->
            <div
              class="study-planning-section"
              v-if="
                studyPlanData.studyPlanning &&
                studyPlanData.studyPlanning.stages.length > 0
              "
            >
              <!-- 主标题 -->
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">03</span>
                  <span class="step-text">{{
                    studyPlanData.studyPlanning.title
                  }}</span>
                </div>
              </div>

              <!-- 阶段循环输出 -->
              <div
                v-for="(stage, stageIndex) in studyPlanData.studyPlanning
                  .stages"
                :key="stage.id"
                class="stage-section"
              >
                <!-- 阶段标题 -->
                <div class="stage-title">{{ stage.title }}</div>

                <div class="stage-content">
                  <!-- 模块容器循环输出 -->
                  <div
                    v-for="(module, moduleIndex) in stage.modules"
                    :key="module.id"
                    class="module-container"
                  >
                    <!-- 模块标题 -->
                    <div class="module-title">
                      <span class="module-title-text">{{ module.name }}</span>
                    </div>

                    <!-- 学习内容 -->
                    <div class="study-item" v-if="module.studyContent">
                      <div class="study-item-title">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="菱形图标"
                        />
                        <span class="study-item-title-text">学习内容</span>
                      </div>
                      <p class="study-item-content">
                        {{ module.studyContent }}
                      </p>
                    </div>

                    <!-- 学习方法 -->
                    <div class="study-item" v-if="module.studyMethod">
                      <div class="study-item-title">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="菱形图标"
                        />
                        <span class="study-item-title-text">学习方法</span>
                      </div>
                      <p class="study-item-content">{{ module.studyMethod }}</p>
                    </div>

                    <!-- 资料推荐 -->
                    <div class="study-item" v-if="module.studyMaterials">
                      <div class="study-item-title">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="菱形图标"
                        />
                        <span class="study-item-title-text">资料推荐</span>
                      </div>
                      <p class="study-item-content">
                        {{ module.studyMaterials }}
                      </p>
                    </div>

                    <!-- 要点提醒 -->
                    <div class="study-item" v-if="module.studyReminder">
                      <div class="study-item-title">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="菱形图标"
                        />
                        <span class="study-item-title-text">要点提醒</span>
                      </div>
                      <p class="study-item-content">
                        {{ module.studyReminder }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 综合建议模块 -->
            <div
              class="comprehensive-advice-section"
              v-if="studyPlanData.comprehensiveAdvice"
            >
              <!-- 建议标题 -->
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">04</span>
                  <span class="step-text">综合建议</span>
                </div>
              </div>
              <!-- 建议内容 -->
              <div class="advice-content">
                <div class="advice-content-container">
                  <p class="advice-text">
                    {{ studyPlanData.comprehensiveAdvice }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- PDF生成按钮 -->
    <div class="pdf-generate-container">
      <button
        class="generate-pdf-btn"
        @click="handleGeneratePdf"
        :disabled="isGeneratingPdf"
      >
        <span v-if="!isGeneratingPdf">生成PDF</span>
        <span v-else>生成中...</span>
      </button>
    </div>

    <div class="print-only print-header">
      <div
        class="top-green-section"
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 60px;
          padding: 10px;
          box-sizing: border-box;
        "
      >
        <div
          class="left-top-container"
          style="display: flex; align-items: center"
        >
          <img
            class="right-color-block"
            style="width: 82px; height: 34px"
            src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/pdf-top-green.png"
            alt="顶部文字"
          />
          <img
            class="text-img"
            style="margin-left: 10px; width: 206px; height: 34px"
            src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/top-text.png"
            alt="顶部文字"
          />
        </div>
        <div class="content-logo">
          <img
            style="height: 50px"
            src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/pdf_logo.png"
            alt="Logo"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, nextTick, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import type { ECharts, EChartsOption } from "echarts";
import {
  getStudyPlanFromDatabase,
  getTargetScores,
  getReportBasicInfo,
  getReportDetailNew,
} from "@/api/report";
import { getNationalLineData } from "@/api/school";

import type {
  SchoolListItem,
  RecommendSchool,
  HighRecommendSchool,
} from "@/types";
import { PreserveStylePdfGenerator } from "@/utils/preserveStylePdfGenerator";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";
const currentRoute = useRoute();
onMounted(() => {
  const main = document.querySelector(".main") as HTMLElement;
  console.log(main);
  const body = document.querySelector("body") as HTMLElement;
  body.style.backgroundColor = "#FFF";

  initPdfData(
    currentRoute.params.report_id as string,
    currentRoute.params.firstLevelcode as string
  );
});
// 学习计划相关接口
interface WeakModuleAnalysis {
  id?: number;
  subject: string;
  problemAnalysis: string;
  solutions: string;
}

interface StudyModule {
  id: string;
  dbId?: number;
  name: string;
  studyContent: string;
  studyMethod: string;
  studyMaterials: string;
  studyReminder: string;
}

interface StudyStage {
  id: string;
  title: string;
  modules: StudyModule[];
}

interface StudyPlanning {
  title: string;
  stages: StudyStage[];
}

interface StudyPlanData {
  weakModuleAnalysis: WeakModuleAnalysis[];
  studyPlanning: StudyPlanning;
  comprehensiveAdvice: string;
  comprehensiveAdviceId?: number;
}

interface TargetScoreData {
  politics: number;
  english: number;
  business1: number;
  business2: number;
  total: number;
}

interface NationalLineData {
  subject_code: string;
  subject_name: string;
  years: string[];
  a_total: number[];
  a_single_100: number[];
  a_single_over100: number[];
  b_total: number[];
  b_single_100: number[];
  b_single_over100: number[];
}

// 学习计划相关接口
interface WeakModuleAnalysis {
  id?: number;
  subject: string;
  problemAnalysis: string;
  solutions: string;
}

interface StudyModule {
  id: string;
  dbId?: number;
  name: string;
  studyContent: string;
  studyMethod: string;
  studyMaterials: string;
  studyReminder: string;
}

interface StudyStage {
  id: string;
  title: string;
  modules: StudyModule[];
}

interface StudyPlanning {
  title: string;
  stages: StudyStage[];
}

interface StudyPlanData {
  weakModuleAnalysis: WeakModuleAnalysis[];
  studyPlanning: StudyPlanning;
  comprehensiveAdvice: string;
  comprehensiveAdviceId?: number;
}

interface TargetScoreData {
  politics: number;
  english: number;
  business1: number;
  business2: number;
  total: number;
}

interface NationalLineData {
  subject_code: string;
  subject_name: string;
  years: string[];
  a_total: number[];
  a_single_100: number[];
  a_single_over100: number[];
  b_total: number[];
  b_single_100: number[];
  b_single_over100: number[];
  first_level_discipline?: string;
}

// 实际数据
const schoolListData = ref<SchoolListItem[]>([]);
const highRecommendData = ref<HighRecommendSchool[]>([]);
//根据学校名称 获取分数差
const getDiffScore = (school_info_id: number) => {
  const school = schoolListData.value.find((item) => item.id == school_info_id);
  return school?.score_diff;
};
interface undergraduateTranscriptItem {
  id: number;
  title: string;
  score: string;
}
// 报告表单数据
const reportForm = reactive({
  name: "", // 学员姓名
  sex: "", // 性别
  undergraduateSchool: "", // 本科院校
  undergraduateMajor: "", // 本科专业
  targetMajor: "", // 目标专业
  majorCode: "", // 专业代码
  phone: "", // 联系方式
  examYear: "", // 考研年份
  isMultiDisciplinary: "", // 跨专业
  educationalStyle: "", // 培养方式
  englishScore: "", // 高考英语成绩
  mathScore: "", // 高考数学成绩
  undergraduateTranscript: [] as undergraduateTranscriptItem[],
  cet4: "", // 大学四级
  cet6: "", // 大学六级
  tofelScore: "", // 托福
  ieltsScore: "", // 雅思
  englishLevel: "", // 英语能力
  region: [] as string[], // 地区倾向
  intendedSchools: [] as string[], // 省份选择
  targetSchool: "", // 梦校
  targetSchoolName: "", // 梦校名称
  schoolLevel: "", // 院校层次
  referenceBooks: "", // 专业课指定参考书
  politics: "", // 政治成绩
  englishS: "", // 英语成绩
  englishType: "", // 业务课一成绩
  mathType: "", // 业务课二成绩
  professional: "", // 专业课成绩
  totalScore: "", // 总分
  personalNeeds: "", // 个性化需求
  weakModules: "", // 薄弱模块
});

// 学习计划数据
const studyPlanData = ref<StudyPlanData | null>(null);

// 目标分数数据
const targetScoreData = ref<TargetScoreData | null>(null);

// 国家线数据
const nationalLineData = ref<NationalLineData | null>(null);

// 当前报告ID
const currentReportId = ref<number | string>("");

// 真实的院校推荐数据
const realRecommendData = ref<RecommendSchool[]>([]);

// 图表引用
const chartA = ref<HTMLDivElement>();
const chartB = ref<HTMLDivElement>();
let chartInstanceA: ECharts | null = null;
let chartInstanceB: ECharts | null = null;

// PDF生成相关
const isGeneratingPdf = ref(false);

// Loading 相关状态
const isLoading = ref(false);
const loadingProgress = ref(0);
const loadingTasks = ref<string[]>([]);
const completedTasks = ref<string[]>([]);

const truncateText = (str: string, maxLength: number = 6): string => {
  return str.length > maxLength ? str.slice(0, maxLength) + "..." : str;
};

let firstLevelDiscipline = "";
// Loading 进度管理函数
const updateLoadingProgress = () => {
  if (loadingTasks.value.length === 0) {
    loadingProgress.value = 0;
    return;
  }

  const progress =
    (completedTasks.value.length / loadingTasks.value.length) * 100;
  loadingProgress.value = Math.round(progress);

  // 如果所有任务完成，延迟一下再隐藏loading
  if (completedTasks.value.length === loadingTasks.value.length) {
    setTimeout(() => {
      isLoading.value = false;
    }, 500);
  }
};

const startLoading = (tasks: string[]) => {
  isLoading.value = true;
  loadingTasks.value = [...tasks];
  completedTasks.value = [];
  loadingProgress.value = 0;
};

const completeTask = (taskName: string) => {
  if (!completedTasks.value.includes(taskName)) {
    completedTasks.value.push(taskName);
    updateLoadingProgress();
  }
};

//处理一级专业括号和数字
const filterMajor = (str: string | undefined) => {
  if (str) {
    return str.replace(/\s*\(\d+\)/, "");
  } else {
    return "";
  }
};
// 生成图表配置 - 使用真实国家线数据
const chartOption = (isARegion: boolean = true): EChartsOption => {
  console.log(`生成${isARegion ? "A区" : "B区"}图表配置`);

  // 如果没有传入国家线数据，使用默认数据
  const nationalData = nationalLineData.value || {
    subject_code: "",
    subject_name: "",
    years: [],
    a_total: [],
    a_single_100: [],
    a_single_over100: [],
    b_total: [],
    b_single_100: [],
    b_single_over100: [],
  };

  console.log("国家线数据:", nationalData);

  // 根据区域选择对应的数据
  const years: string[] =
    nationalData.years.length > 0
      ? nationalData.years
      : ["2021", "2022", "2023", "2024", "2025"];

  const totalScores: number[] = isARegion
    ? nationalData.a_total.length > 0
      ? nationalData.a_total
      : [360, 370, 360, 370, 370]
    : nationalData.b_total.length > 0
    ? nationalData.b_total
    : [340, 350, 340, 350, 350];

  const single100Scores: number[] = isARegion
    ? nationalData.a_single_100.length > 0
      ? nationalData.a_single_100
      : [60, 65, 60, 65, 65]
    : nationalData.b_single_100.length > 0
    ? nationalData.b_single_100
    : [55, 60, 55, 60, 60];

  const singleOver100Scores: number[] = isARegion
    ? nationalData.a_single_over100.length > 0
      ? nationalData.a_single_over100
      : [90, 95, 90, 95, 95]
    : nationalData.b_single_over100.length > 0
    ? nationalData.b_single_over100
    : [85, 90, 85, 90, 90];

  console.log(`${isARegion ? "A区" : "B区"}图表数据:`, {
    years,
    totalScores,
    single100Scores,
    singleOver100Scores,
  });

  const option: EChartsOption = {
    title: {
      left: "left",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
        color: "#1bb394",
      },
      top: 5,
    },
    grid: {
      left: 20,
      right: 20,
      top: 50,
      bottom: 10,
      containLabel: true,
    },
    legend: {
      data: ["总分", "单科(满分=100)", "单科(满分>100)"],
      right: 10,
      top: 15,
      icon: "rect",
      itemWidth: 16,
      itemHeight: 8,
      textStyle: {
        fontSize: 12,
      },
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0,0,0,0.7)",
      borderRadius: 8,
      textStyle: { color: "#fff" },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: years,
      axisLine: {
        show: true,
        lineStyle: { color: "#1bb394" },
      },
      axisLabel: {
        color: "#666",
        fontSize: 12,
      },
    },
    yAxis: {
      type: "value",
      offset: 5,
      min: 0,
      max: 400,
      interval: 50,
      splitLine: {
        show: true,
        lineStyle: {
          color: "#eee",
          type: "dashed",
        },
      },
      axisLine: { show: false },
      axisLabel: {
        color: "#666",
        fontSize: 12,
      },
    },
    series: [
      {
        name: "总分",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(255, 153, 0, 0.3)", // 顶部颜色
              },
              {
                offset: 1,
                color: "rgba(255, 153, 0, 0.1)", // 底部颜色
              },
            ],
            global: false,
          },
        },
        lineStyle: {
          color: "#ff9900",
          width: 2,
        },
        itemStyle: {
          color: "#ff9900",
          borderWidth: 2,
          borderColor: "#fff",
        },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#ff9900",
          fontWeight: "bold",
          offset: [0, -8],
        },
        data: totalScores,
      },
      {
        name: "单科(满分=100)",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(27, 179, 148, 0.3)", // 顶部颜色
              },
              {
                offset: 1,
                color: "rgba(27, 179, 148, 0.1)", // 底部颜色
              },
            ],
            global: false,
          },
        },
        lineStyle: {
          color: "#1bb394",
          width: 2,
        },
        itemStyle: {
          color: "#1bb394",
          borderWidth: 2,
          borderColor: "#fff",
        },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#1bb394",
          fontWeight: "bold",
          offset: [0, -8],
        },
        data: single100Scores,
      },
      {
        name: "单科(满分>100)",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(255, 99, 132, 0.3)", // 顶部颜色
              },
              {
                offset: 1,
                color: "rgba(255, 99, 132, 0.1)", // 底部颜色
              },
            ],
            global: false,
          },
        },
        lineStyle: {
          color: "#ff6384",
          width: 2,
        },
        itemStyle: {
          color: "#ff6384",
          borderWidth: 2,
          borderColor: "#fff",
        },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#ff6384",
          fontWeight: "bold",
          offset: [0, -20],
        },
        data: singleOver100Scores,
      },
    ],
  };

  console.log("生成的图表配置:", option);
  return option;
};

// 初始化图表
const initCharts = (): void => {
  console.log("initCharts 被调用");

  setTimeout(() => {
    console.log("开始初始化图表...");
    console.log("chartA.value:", chartA.value);
    console.log("chartB.value:", chartB.value);

    if (chartA.value) {
      const rect = chartA.value.getBoundingClientRect();
      console.log("A区图表容器尺寸:", rect);

      if (rect.width > 0 && rect.height > 0) {
        try {
          if (chartInstanceA) {
            console.log("销毁已存在的A区图表");
            chartInstanceA.dispose();
          }
          console.log("开始初始化A区图表");
          chartInstanceA = echarts.init(chartA.value);
          const optionA = chartOption(true);
          console.log("A区图表配置:", optionA);
          chartInstanceA.setOption(optionA);
          console.log("A区图表初始化成功");
        } catch (error) {
          console.error("A区图表初始化失败:", error);
        }
      } else {
        console.warn("A区图表容器尺寸为0，延迟重试");
        setTimeout(() => initCharts(), 500);
        return;
      }
    } else {
      console.warn("A区图表容器不存在");
    }

    if (chartB.value) {
      const rect = chartB.value.getBoundingClientRect();
      console.log("B区图表容器尺寸:", rect);

      if (rect.width > 0 && rect.height > 0) {
        try {
          if (chartInstanceB) {
            console.log("销毁已存在的B区图表");
            chartInstanceB.dispose();
          }
          console.log("开始初始化B区图表");
          chartInstanceB = echarts.init(chartB.value);
          const optionB = chartOption(false);
          console.log("B区图表配置:", optionB);
          chartInstanceB.setOption(optionB);
          console.log("B区图表初始化成功");
        } catch (error) {
          console.error("B区图表初始化失败:", error);
        }
      } else {
        console.warn("B区图表容器尺寸为0，延迟重试");
        setTimeout(() => initCharts(), 500);
        return;
      }
    } else {
      console.warn("B区图表容器不存在");
    }
  }, 500); // 增加延迟时间
};

// 处理窗口大小变化
const handleResize = (): void => {
  setTimeout(() => {
    if (chartInstanceA) {
      chartInstanceA.resize();
    }
    if (chartInstanceB) {
      chartInstanceB.resize();
    }

    // 如果图表未初始化，重新初始化
    if (!chartInstanceA || !chartInstanceB) {
      initCharts();
    }
  }, 100);
};

// 加载学习计划数据
const loadStudyPlanData = async (reportId: number | string) => {
  try {
    console.log("开始加载学习计划数据，报告ID:", reportId);
    const response = await getStudyPlanFromDatabase({ report_id: reportId });
    console.log("学习计划API响应:", response);

    if (response.code === 0 && response.data) {
      studyPlanData.value = response.data;
      console.log("学习计划数据加载成功:", studyPlanData.value);
    } else {
      console.warn("学习计划数据加载失败:", response.msg);
    }
  } catch (error) {
    console.error("加载学习计划数据失败:", error);
  } finally {
    completeTask("学习计划数据");
  }
};

// 加载目标分数数据
const loadTargetScoreData = async (reportId: number | string) => {
  try {
    console.log("开始加载目标分数数据，报告ID:", reportId);
    const response = await getTargetScores(reportId);
    console.log("目标分数API响应:", response);

    if (response.code === 0 && response.data) {
      targetScoreData.value = response.data;
      console.log("目标分数数据加载成功:", targetScoreData.value);
    } else {
      console.warn("目标分数数据加载失败:", response.msg);
    }
  } catch (error) {
    console.error("加载目标分数数据失败:", error);
  } finally {
    completeTask("目标分数数据");
  }
};

// 加载国家线数据
const loadNationalLineData = async (majorCode: string) => {
  try {
    console.log("开始加载国家线数据，专业代码:", majorCode);
    const response = await getNationalLineData(majorCode);
    console.log("国家线数据API响应:", response);

    if (response.code === 0 && response.data) {
      nationalLineData.value = response.data;
      console.log("国家线数据加载成功:", nationalLineData.value);

      // 重新初始化图表
      setTimeout(() => {
        initCharts();
      }, 100);
    } else {
      console.warn("国家线数据加载失败:", response.msg);
    }
  } catch (error) {
    console.error("加载国家线数据失败:", error);
  } finally {
    completeTask("国家线数据");
  }
};

// 加载院校推荐数据
const loadRecommendationData = async (reportId: number | string) => {
  try {
    console.log("开始加载院校推荐数据，报告ID:", reportId);
    const response = await getReportDetailNew({ report_id: reportId });
    console.log("院校推荐数据API响应:", response);

    if (response.code === 0 && response.data) {
      const { recommend_list, high_recommend_list, school_list } =
        response.data;

      // 更新真实推荐数据
      if (recommend_list && Array.isArray(recommend_list)) {
        console.log("原始推荐数据:", recommend_list);
        realRecommendData.value = recommend_list.map((school: any) => {
          console.log("处理学校数据:", school.school_name, school.basic_info);
          return {
            school_id: school.school_id,
            school_name: school.school_name,
            major_name: school.major_name,
            difficulty_analysis: school.difficulty_analysis,
            suggest: school.suggest,
            reason: school.reason,
            basic_info: school.basic_info || {},
            school_info: school.school_info || {},
            admission_stats: school.admission_stats || { has_data: false },
            retest_stats: school.retest_stats || { has_data: false },
            current_year_retest_list: school.current_year_retest_list || {
              list: [],
              year: null,
              count: 0,
            },
            current_year_admission_list: school.current_year_admission_list || {
              list: [],
              year: null,
              count: 0,
            },
            admission_data: school.admission_data || [],
          };
        });
        console.log(
          "院校推荐数据加载成功，共",
          recommend_list.length,
          "所院校"
        );
      }

      // 更新院校总览数据
      if (school_list && Array.isArray(school_list)) {
        schoolListData.value = school_list;
        console.log("院校总览数据加载成功，共", school_list.length, "所院校");
      }

      // 更新高性价比推荐数据
      if (high_recommend_list) {
        highRecommendData.value = [high_recommend_list];
        console.log(
          "高性价比推荐数据加载成功:",
          high_recommend_list.school_name
        );
      }
    } else {
      console.warn("院校推荐数据加载失败:", response.msg);
    }
  } catch (error) {
    console.error("加载院校推荐数据失败:", error);
  } finally {
    completeTask("院校推荐数据");
  }
};

// 加载学员基本信息数据
const loadReportBasicInfo = async (reportId: number | string) => {
  try {
    console.log("开始加载学员基本信息，报告ID:", reportId);
    const response = await getReportBasicInfo(reportId);
    console.log("学员基本信息API响应:", response);

    if (response.code === 0 && response.data) {
      const { student_info, report_info } = response.data;

      // 更新reportForm数据
      if (student_info) {
        reportForm.name = student_info.name || "";
        reportForm.sex = student_info.sexText || "";
        reportForm.undergraduateSchool =
          student_info.undergraduateSchoolName || "";
        reportForm.undergraduateMajor =
          student_info.undergraduateMajorName || "";
        reportForm.targetMajor = report_info.target_major || "";
        reportForm.majorCode = student_info.majorCode || "";
        reportForm.phone = student_info.phone || "";
        reportForm.examYear = student_info.examYear || "";
        reportForm.isMultiDisciplinary = student_info.isCrossMajorText || "";
        reportForm.educationalStyle = student_info.educationalStyleText || "";
        reportForm.englishScore = student_info.englishScore || "";
        reportForm.cet4 = student_info.cet4 || "";
        reportForm.cet6 = student_info.cet6 || "";
        reportForm.tofelScore = student_info.tofelScore || "";
        reportForm.ieltsScore = student_info.ieltsScore || "";
        reportForm.undergraduateTranscript =
          student_info.undergraduateTranscript || [];
        reportForm.englishLevel = student_info.englishAbility || "";
      }

      // 更新报告相关信息
      if (report_info) {
        reportForm.politics = report_info.politics_score || "";
        reportForm.englishS = report_info.english_score || "";
        reportForm.englishType = report_info.english_type || "";
        reportForm.mathType = report_info.math_type || "";
        reportForm.professional = report_info.professional_score || "";
        reportForm.totalScore = report_info.total_score || "";
        reportForm.personalNeeds = report_info.personal_needs || "";
        reportForm.weakModules = report_info.weak_modules || "";

        // 院校信息
        reportForm.region = report_info.region_preference || "";
        reportForm.intendedSchools = report_info.province_selection || "";
        reportForm.targetSchoolName = report_info.dream_school || "";
        reportForm.schoolLevel = report_info.school_level || "";
        reportForm.referenceBooks = report_info.reference_books || "";
      }

      console.log("学员基本信息加载成功，已更新reportForm");
      await loadNationalLineData(firstLevelDiscipline);
    } else {
      console.warn("学员基本信息加载失败:", response.msg);
    }
  } catch (error) {
    console.error("加载学员基本信息失败:", error);
  } finally {
    completeTask("学员基本信息");
  }
};

// 初始化PDF数据（暴露给父组件调用）
const initPdfData = async (
  reportId: number | string,
  firstLevelcode: string
) => {
  currentReportId.value = reportId;
  firstLevelDiscipline = firstLevelcode;
  // 启动loading状态
  const tasks = [
    "学员基本信息",
    "学习计划数据",
    "目标分数数据",
    "院校推荐数据",
    "国家线数据",
  ];
  startLoading(tasks);

  try {
    await Promise.all([
      loadStudyPlanData(reportId),
      loadTargetScoreData(reportId),
      loadReportBasicInfo(reportId),
      loadRecommendationData(reportId),
    ]);
  } catch (error) {
    console.error("加载PDF数据失败:", error);
    // 即使出错也要隐藏loading
    isLoading.value = false;
  }
};

// 处理PDF生成
const handleGeneratePdf = async () => {
  if (isGeneratingPdf.value) return;

  window.print();

  return;

  try {
    isGeneratingPdf.value = true;
    ElMessage.info("正在生成PDF，请稍候...");

    // 获取报告容器元素
    const reportContainer = document.querySelector(
      ".generate-pdf-container .report-container"
    ) as HTMLElement;
    if (!reportContainer) {
      throw new Error("未找到报告容器元素");
    }

    // 生成PDF文件名
    const filename = `AI择校报告_${reportForm.name || "未命名"}_${new Date()
      .toISOString()
      .slice(0, 10)}.pdf`;

    // 使用保持样式的PDF生成器
    await PreserveStylePdfGenerator.generatePdf(reportContainer, {
      filename,
      quality: 0.95,
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      reportId: currentReportId.value, // 传递报告ID
      autoUpload: false, // 启用自动上传
    });

    ElMessage.success("PDF生成成功！");
  } catch (error) {
    console.error("PDF生成失败:", error);
    ElMessage.error("PDF生成失败，请重试");
  } finally {
    isGeneratingPdf.value = false;
  }
};

// 组件挂载后初始化图表
onMounted(() => {
  console.log("组件已挂载，开始初始化图表");
  nextTick(() => {
    initCharts();
    // 添加窗口resize监听
    window.addEventListener("resize", handleResize);

    // 额外的延迟初始化，确保DOM完全渲染
    setTimeout(() => {
      if (!chartInstanceA || !chartInstanceB) {
        console.log("图表未正确初始化，重新尝试");
        initCharts();
      }
    }, 1000);
  });
});

// 清理资源
onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);

  // 销毁图表实例
  if (chartInstanceA) {
    chartInstanceA.dispose();
    chartInstanceA = null;
  }
  if (chartInstanceB) {
    chartInstanceB.dispose();
    chartInstanceB = null;
  }
});

// 暴露方法给父组件
defineExpose({
  initPdfData,
  handleGeneratePdf,
});
</script>

<style scoped lang="less">
.generate-pdf-container {
  background-color: #f5f5f5;
  position: relative;
}
.print-only {
  display: none;
}
/* Loading 遮罩层样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.loading-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(27, 179, 148, 0.2);
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1bb394;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 20px;
  font-weight: 500;
}

.loading-progress {
  width: 300px;
  margin: 0 auto;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1bb394, #16a085);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* PDF生成按钮容器 */
.pdf-generate-container {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

/* PDF生成按钮样式 */
.generate-pdf-btn {
  background: linear-gradient(135deg, #1bb394, #16a085);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(27, 179, 148, 0.3);
  transition: all 0.3s ease;
  min-width: 120px;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #16a085, #1bb394);
    box-shadow: 0 6px 20px rgba(27, 179, 148, 0.4);
    transform: translateY(-2px);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(27, 179, 148, 0.3);
  }

  &:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
  }
}

.report-container {
  width: 800px;
  background: white;
  margin: 0 auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 报告封面头部样式 */
.report-header {
  width: 100%;
  height: 1121px;
  background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/report_pdf_front.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  position: relative;

  .stu-name {
    position: absolute;
    top: 36%;
    left: 6%;
    font-size: 30px;
    font-weight: bold;
  }
}

/* 报告内容页面样式 */
.report-content {
  background: white;
  position: relative;
  /*padding-bottom: 100px;*/
  /* 为footer预留空间 */
}

.step-title-bg {
  width: 100%;
  height: 38px;
  height: auto;
  margin-bottom: 20px;
}

.first-step {
  padding: 0 40px;
  /* margin-top: 30px;*/
}

/* 图表相关样式 - 基于MajorAnalysis.vue */
.chart-section {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  align-items: center;

  .region-label {
    color: #5a5a5a;
    padding: 8px 0;
    border-radius: 4px;
    font-weight: bold;
    margin-right: 20px;
    min-width: 50px;
    text-align: left;
  }

  .chart-info {
    display: flex;
    gap: 20px;

    span {
      font-size: 14px;
      color: #5a5a5a;
    }
  }
}

.chart-container {
  padding: 10px 0;
  background: white;
}

/* 图表样式 - 基于MajorAnalysis.vue */
.echarts-box {
  width: 100%;
  height: 280px;
  border: 1px solid #1bb394;
  border-radius: 12px;
  background: #fff;
  box-sizing: border-box;
  padding: 5px;
}

/* 院校信息表格样式 */
.school-info-section {
  margin-bottom: 40px;
}

.school-info-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  th {
    border: none;
    background: #d8ffed;
    color: #5a5a5a;
    font-weight: 600;
    text-align: center;
    font-size: 12px;
    padding: 27px 0px;
    border: none;
    white-space: nowrap;
    width: 140px;

    &:first-child {
      width: 50px;
      padding-left: 20px;
    }

    &:last-child {
      padding-right: 20px;
    }
  }

  td {
    border: none;
    text-align: center;
    border: none;
    font-size: 12px;
    color: #333;
    vertical-align: middle;
    width: 140px;

    &:first-child {
      padding-left: 20px;
      width: 50px;
      font-weight: 600;
    }

    &:nth-child(2) {
      text-align: center;
      /* 改为居中对齐 */
      padding: 0;
      /* 移除左边距，让内容完全居中 */
      height: 60px;
      /* 给容器设置固定高度 */

      > div {
        padding-top: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        /* 水平居中 */
        justify-content: center;
        /* 垂直居中 */
        gap: 6px;
        height: 100%;
        /* 占满父容器高度 */

        > span {
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  tbody tr:nth-child(odd) {
    background: white;
  }

  tbody tr:nth-child(even) {
    background: #f8f9fa;
  }
}

.school-tags {
  display: flex;
  gap: 4px;
  justify-content: center;
  /* 标签也居中对齐 */
  padding-bottom: 8px;

  .tag {
    display: inline-block;
    border-radius: 4px;
    font-size: 8px;
    font-weight: 500;
    color: white;
    width: 28px;
    height: 18px;
    line-height: 18px;
    text-align: center;

    &.tag-double {
      background: #ff9b3a;
    }

    &.tag-985 {
      background: #caa0ff;
    }

    &.tag-211 {
      background: #92baff;
    }
  }
}

/* 步骤内容样式 */
.step-section {
  margin-bottom: 26px;
}

.step-header {
  position: relative;

  .step-num-tag {
    position: relative;
    display: inline-block;

    img {
      height: 35px;
    }

    .step-number {
      width: 30px;
      height: 30px;
      position: absolute;
      left: 6px;
      line-height: 30px;
      text-align: center;
      top: 50%;
      transform: translateY(-50%);
      color: white;
      font-weight: bold;
      font-size: 16px;
    }

    .step-text {
      position: absolute;
      left: -56px;
      width: 100%;
      text-align: left;
      top: 46%;
      transform: translateY(-50%);
      color: white;
      font-weight: bold;
      font-size: 14px;
      color: #1bb394;
      padding-left: 108px;
    }
  }
}

.step-content {
  border-radius: 8px;
}
.form-undergraduate {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  .form-item {
    width: 31%;
  }
}
.form-grid {
  .form-row {
    display: flex;
    margin-bottom: 15px;
    gap: 20px;

    &.full-width {
      .form-item {
        flex: 1;
      }
    }

    .form-item {
      flex: 1;
      display: flex;
      align-items: center;

      .label {
        color: #5a5a5a;
        font-weight: bold;
        margin-right: 8px;
        white-space: nowrap;
      }

      .value {
        color: #666;
        flex: 1;
      }

      &.full-width {
        flex: 1 1 100%;
      }
    }
  }
}

/* 成绩表格样式 */
.score-table {
  margin-bottom: 20px;
  border: 1px solid #1bb394;
  overflow: hidden;
  border-radius: 8px;

  table {
    width: 100%;
    border-collapse: collapse;
    background: white;

    th,
    td {
      padding: 12px 15px;
      text-align: center;
      border-left: 1px solid #1bb394;

      &:first-child {
        border-left: none;
      }
    }

    th {
      background: #f5fffd;
      color: #333;
      font-weight: 600;
      border-bottom: 1px solid #1bb394;
    }

    td {
      color: #333;
    }
  }
}

.weak-modules {
  border: 1px solid #1bb394;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    color: #5a5a5a;
    font-weight: 600;
    margin-right: 8px;
  }

  .value {
    color: #5a5a5a;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-header {
    height: auto;
    min-height: 400px;

    .header-content {
      flex-direction: column;
      text-align: center;
      padding: 30px 20px;

      .logo-section {
        margin-bottom: 20px;

        .header-logo {
          height: 60px;
        }
      }

      .title-section {
        padding: 0;
        margin-bottom: 20px;

        .report-title {
          font-size: 28px;
        }

        .report-subtitle {
          font-size: 14px;
        }
      }

      .decoration-section {
        .decoration-image {
          height: 80px;
        }
      }
    }
  }

  .steps {
    padding: 20px;
  }
}

/* 新增样式 */
.step-num-tag {
  background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitlebg.png");
  width: 265px;
  height: 40px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  margin-bottom: 14px;
}

// .step-num-tag span {
//   width: 30px;
//   height: 30px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   color: white;
//   border-radius: 50%;
//   font-weight: bold;
//   padding-left: 13px;
// }

.tag-text {
  color: #1bb394;
  font-weight: bold;
}

/* 院校详情卡片样式 */
.school-detail-card {
  background-color: #fff;
  overflow: hidden;
}

.school-header {
  display: flex;
}

.school-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
  border: 1px solid #e0e0e0;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 90%;
    height: 90%;
    object-fit: contain;
  }
}

.school-info {
  flex: 1;
}

.school-title {
  display: flex;
  align-items: baseline;

  h2 {
    font-size: 22px;
    color: #333;
    margin: 0;
    margin-right: 12px;
  }

  .school-location {
    font-size: 18px;
    color: #5a5a5a;
  }
}

.school-tags-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;

  .tag {
    display: inline-block;
    width: 52px;
    height: 25px;
    border-radius: 4px;
    color: #fff;
    text-align: center;
    line-height: 25px;
    font-size: 14px;
    margin-right: 5px;

    &.tag-double {
      background-color: #ff9b3a;
    }

    &.tag-985 {
      background-color: #caa0ff;
    }

    &.tag-211 {
      background-color: #92baff;
    }
  }

  .major-name,
  .score-diff {
    padding: 10px;
    font-weight: normal;
    font-size: 18px;
    color: #5a5a5a;
    line-height: 36px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .score-diff {
    margin-left: -16px;
  }
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  position: relative;
  margin-bottom: 15px;
}

.school-detail-section {
  margin-bottom: 20px;
  padding: 10px 20px 20px;
  border-radius: 12px 12px 12px 12px;
  border: 1px dashed #2fc293;
  background: linear-gradient(180deg, #e6f5ef 0%, rgba(255, 255, 255, 0) 40%);
}

.detail-item {
  display: flex;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.item-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.item-content {
  flex: 1;

  h4 {
    font-size: 16px;
    color: #333;
    margin: 0 0 8px 0;
    font-weight: 600;
  }

  p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin: 0 0 5px 0;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 招生情况样式 */
.step-content-zs {
  width: 100%;
  margin-top: 20px;
}

.admission-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 20px 0 15px 0;
  position: relative;
  padding-left: 12px;
}

.admission-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
}

.admission-table th {
  background-color: #dcf7f0;
  color: #333;
  font-weight: bold;
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #e8f5f0;
}

.admission-table td {
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #e8f5f0;
  background-color: #fff;
}

.admission-table,
.admission-table th,
.admission-table td {
  border: none !important;
}

.admission-table tr:nth-child(even) td {
  background-color: #f9f9f9;
}

.reexam-container {
  border: 1px dashed #2fc293;
  background: linear-gradient(180deg, #e6f5ef 0%, rgba(255, 255, 255, 0) 40%);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
}

.reexam-card {
  padding: 0 20px 20px;
}

.reexam-card:last-child {
  border-bottom: none;
}

.reexam-header {
  display: flex;
  align-items: center;
  padding: 15px 0;

  img {
    width: 27px;
    height: 21px;
    margin-right: 8px;
  }
}

.reexam-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.reexam-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.reexam-content p {
  margin: 8px 0;
}

.recommend-school-wraper {
  /* 推荐院校包装器样式 */
}

/* 推荐综合性价比高的院校样式 */
.recommend-school-container {
  margin-top: 28px;
  border: 1px dashed #2fc293;
  background: linear-gradient(180deg, #e6f5ef 0%, rgba(255, 255, 255, 0) 40%);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
  background-color: #fff;
}

.recommend-school-card {
  padding: 0 20px 20px;
}

.recommend-school-header {
  display: flex;
  align-items: center;
  padding: 15px 0;
}

.recommend-icon {
  margin-right: 8px;

  img {
    width: 27px;
    height: 21px;
  }
}

.recommend-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.recommend-school-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.recommend-school-content p {
  margin: 8px 0;
}
.recommend-school-wraper {
  .step-num-tag {
    position: relative;
    .step-number {
      position: absolute;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: white;
      font-weight: bold;
      font-size: 16px;
      border-radius: 50%;
    }
    .tag-text {
      padding-left: 57px;
    }
  }
}

/* 学习计划相关样式 */
.weak-module-container {
  border: 1px dashed #1bb394;
  background: linear-gradient(180deg, #e6f5ef 0%, rgba(255, 255, 255, 0) 40%);
  border-radius: 10px;
  margin-bottom: 30px;
  padding: 20px 0;
}

.weak-module-item {
  padding: 0 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-section,
.analysis-section,
.solutions-section {
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 0;
  }
}

.subtitle {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  img {
    margin-right: 8px;
  }

  .subtitle-text {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }
}

.detail-text,
.analysis-text,
.solution-text {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
  margin: 0;
  padding-left: 34px;
}

/* 目标分数表格样式 */
.target-score-section {
  margin-bottom: 20px;
  padding: 0 20px;
}

.target-score-header {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 15px;

  .target-score-title {
    height: 20px;
    line-height: 20px;
    font-weight: bold;
    font-size: 16px;
    color: #5a5a5a;
  }
}

.target-score-table {
  border: 1px solid #1bb394;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.score-row {
  display: flex;
}

.header-row {
  background: #f5fffd;
}

.score-cell {
  flex: 1;
  padding: 12px 15px;
  text-align: center;
  border-right: 1px solid #1bb394;
  font-weight: 600;
  color: #333;

  &:last-child {
    border-right: none;
  }
}

.bottom-cell {
  border-top: 1px solid #1bb394;
  background: white;
  font-weight: normal;
}

/* 学习规划样式 */
.study-planning-section {
  margin-bottom: 30px;
}

.stage-section {
  margin-bottom: 25px;

  &:last-child {
    margin-bottom: 0;
  }
}

.stage-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}
.stage-content {
  padding: 20px 0;
  border: 1px dashed #2fc293;
  border-radius: 10px;
  background: linear-gradient(180deg, #e6f5ef 0%, rgba(255, 255, 255, 0) 40%);
}
.module-container {
  margin-bottom: 20px;
  padding: 0 20px;

  border-radius: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.module-title {
  margin-bottom: 15px;
  padding-bottom: 8px;

  .module-title-text {
    font-size: 16px;
    font-weight: bold;
  }
}

.study-item {
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 0;
  }
}

.study-item-title {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  img {
    margin-right: 8px;
  }

  .study-item-title-text {
    font-size: 16px;
    color: #4c5370;
    font-weight: bold;
  }
}

.study-item-content {
  font-size: 14px;
  color: #504e4e;
  line-height: 24px;
  margin: 0;
  white-space: pre-wrap; /* 保持换行符和空格 */
}

/* 综合建议样式 */
.comprehensive-advice-section {
  margin-bottom: 30px;
  white-space: pre-wrap;
}

.advice-content {
  margin-top: 15px;
}

.advice-content-container {
  padding: 20px;
  border: 1px dashed #2fc293;
  background: linear-gradient(180deg, #e6f5ef 0%, rgba(255, 255, 255, 0) 40%);
  border-radius: 8px;
}

.advice-text {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
  margin: 0;
}
</style>
<style scoped>
.print-only {
  display: none;
}
@media print {
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  body {
    height: auto;
    background-color: #fff;
    margin: 0;
    padding: 0;
  }

  .generate-pdf-container {
    min-height: 100vh;
    background-color: #fff;
  }

  /* 隐藏屏幕显示的元素 */
  .pdf-generate-container {
    display: none;
  }

  .report-header {
    /* 在打印环境中，确保正确的尺寸 */
    width: 100%;
    height: 297mm; /* A4纸张高度 */
    min-height: 297mm;

    /* 强制浏览器打印背景图和颜色 */
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;

    /* 背景图片设置 */
    background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/report_pdf_front.png");
    background-repeat: no-repeat;
    background-size: 100% 100%; /* 拉伸以填满整个页面 */
    background-position: center;
    position: relative;

    /* 确保在打印时显示完整 */
    page-break-inside: avoid;
    page-break-after: always;
  }

  /* 页面设置 */
  @page {
    margin-bottom: 60px;
    margin-top: 80px;
    margin-left: 0;
    margin-right: 0;

    size: A4;
  }
  /* 页面计数器 */
  @page {
    @bottom-right {
      content: counter(page) " / " counter(pages);
      display: flex;
      align-items: center;
      justify-content: center;
      height: 20px;
      text-align: center;
      width: 70px;
      margin-right: 20px;
      padding-left: 10px;
      margin-top: 10px;
      background-image: url("data:image/png;base64,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");
      background-repeat: no-repeat;
    }
    @top-left {
      content: "";
      display: block;
      height: 80px;
      width: 100%;
      background-image: url("data:image/jpeg;base64,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");
      background-repeat: no-repeat;
    }
  }

  @page :first {
    margin: 0;
    size: A4;
    @top-left {
      content: ""; /* 将第一页的 @top-left 内容设置为空字符串 */
    }
  }
}
html,
body {
  background-color: #fff !important;
}
</style>
